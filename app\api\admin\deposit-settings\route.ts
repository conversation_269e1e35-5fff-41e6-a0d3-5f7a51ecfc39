import { withAdminAuth } from "@/lib/admin-auth";
import {
	DepositSettings,
	getDepositSettings,
	updateDepositSettings,
	validateDepositPercentage,
} from "@/lib/deposit-settings";
import { NextRequest, NextResponse } from "next/server";

// GET /api/admin/deposit-settings - Get current deposit settings
export const GET = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const settings = await getDepositSettings();

		return NextResponse.json({
			success: true,
			settings,
		});
	} catch (error) {
		console.error("Error fetching deposit settings:", error);
		return NextResponse.json({ error: "Failed to fetch deposit settings" }, { status: 500 });
	}
});

// PUT /api/admin/deposit-settings - Update deposit settings
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const body = await request.json();
		const { depositPercentage, isDepositEnabled, minimumDepositAmount, maximumDepositAmount } = body;

		// Validate deposit percentage if provided
		if (depositPercentage !== undefined) {
			const validation = validateDepositPercentage(depositPercentage);
			if (!validation.isValid) {
				return NextResponse.json({ error: validation.error }, { status: 400 });
			}
		}

		// Validate minimum and maximum amounts
		if (minimumDepositAmount !== undefined && minimumDepositAmount < 0) {
			return NextResponse.json({ error: "Minimum deposit amount cannot be negative" }, { status: 400 });
		}

		if (maximumDepositAmount !== undefined && maximumDepositAmount < 0) {
			return NextResponse.json({ error: "Maximum deposit amount cannot be negative" }, { status: 400 });
		}

		if (
			minimumDepositAmount !== undefined &&
			maximumDepositAmount !== undefined &&
			minimumDepositAmount > maximumDepositAmount
		) {
			return NextResponse.json(
				{ error: "Minimum deposit amount cannot be greater than maximum deposit amount" },
				{ status: 400 }
			);
		}

		// Update settings
		const updateData: Partial<DepositSettings> = {};

		if (depositPercentage !== undefined) {
			updateData.depositPercentage = depositPercentage;
		}

		if (isDepositEnabled !== undefined) {
			updateData.isDepositEnabled = isDepositEnabled;
		}

		if (minimumDepositAmount !== undefined) {
			updateData.minimumDepositAmount = minimumDepositAmount;
		}

		if (maximumDepositAmount !== undefined) {
			updateData.maximumDepositAmount = maximumDepositAmount;
		}

		const success = await updateDepositSettings(updateData);

		if (!success) {
			return NextResponse.json({ error: "Failed to update deposit settings" }, { status: 500 });
		}

		// Return updated settings
		const updatedSettings = await getDepositSettings();

		return NextResponse.json({
			success: true,
			settings: updatedSettings,
			message: "Deposit settings updated successfully",
		});
	} catch (error) {
		console.error("Error updating deposit settings:", error);
		return NextResponse.json({ error: "Failed to update deposit settings" }, { status: 500 });
	}
});

// POST /api/admin/deposit-settings/reset - Reset to default settings
export const POST = withAdminAuth(async (request: NextRequest) => {
	try {
		const { action } = await request.json();

		if (action !== "reset") {
			return NextResponse.json({ error: 'Invalid action. Use "reset" to reset settings.' }, { status: 400 });
		}

		const success = await updateDepositSettings({
			depositPercentage: 20, // Default 20%
			isDepositEnabled: true,
			minimumDepositAmount: undefined,
			maximumDepositAmount: undefined,
		});

		if (!success) {
			return NextResponse.json({ error: "Failed to reset deposit settings" }, { status: 500 });
		}

		const settings = await getDepositSettings();

		return NextResponse.json({
			success: true,
			settings,
			message: "Deposit settings reset to defaults",
		});
	} catch (error) {
		console.error("Error resetting deposit settings:", error);
		return NextResponse.json({ error: "Failed to reset deposit settings" }, { status: 500 });
	}
});
