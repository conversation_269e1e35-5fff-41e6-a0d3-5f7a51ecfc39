"use client";

import AdminLayout from "@/components/admin/AdminLayout";
import { EmployeeAvailabilityManager } from "@/components/admin/EmployeeAvailabilityManager";
import { Card, CardContent } from "@/components/ui/card";
import { Clock } from "lucide-react";

export default function EmployeeAvailabilityPage() {
	return (
		<AdminLayout>
			<div className="space-y-6">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Disponibilités des Employés</h1>
					<p className="text-gray-600">G<PERSON>rez les horaires de travail de vos employés</p>
				</div>

				{/* Info Card */}
				<Card className="border-blue-200 bg-blue-50">
					<CardContent className="p-4">
						<div className="flex items-start gap-3">
							<Clock className="w-5 h-5 text-blue-600 mt-0.5" />
							<div>
								<h3 className="font-medium text-blue-900">Système de disponibilité</h3>
								<p className="text-sm text-blue-700 mt-1">
									Configurez les horaires de travail de vos employés par jour de la semaine. Les
									employés sans horaires configurés utilisent les horaires par défaut (8h-18h).
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Main Component */}
				<EmployeeAvailabilityManager />
			</div>
		</AdminLayout>
	);
}
