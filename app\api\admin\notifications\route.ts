import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Notification = Database["public"]["Tables"]["notifications"]["Row"];
type NotificationInsert = Database["public"]["Tables"]["notifications"]["Insert"];
type NotificationUpdate = Database["public"]["Tables"]["notifications"]["Update"];

// GET /api/admin/notifications - Get notifications with filtering and pagination
export const GET = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const filter = searchParams.get("filter") || "all"; // all, unread, read, type
		const type = searchParams.get("type");
		const priority = searchParams.get("priority");
		const offset = (page - 1) * limit;

		// Build query
		let query = supabaseAdmin
			.from("notifications")
			.select(
				`
        *,
        recipient:profiles!recipient_id(first_name, last_name, email),
        reservation:reservations(id, qr_code)
      `
			)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (filter === "unread") {
			query = query.eq("is_read", false);
		} else if (filter === "read") {
			query = query.eq("is_read", true);
		}

		if (type) {
			query = query.eq("notification_type", type);
		}

		if (priority) {
			query = query.eq("priority", priority);
		}

		const { data: notifications, error, count } = await query;

		if (error) {
			console.error("Error fetching notifications:", error);
			return NextResponse.json({ error: "Failed to fetch notifications" }, { status: 500 });
		}

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin
			.from("notifications")
			.select("*", { count: "exact", head: true });

		return NextResponse.json({
			success: true,
			data: notifications,
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Notifications GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "notifications:read");

// POST /api/admin/notifications - Create new notification
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const body = await request.json();
		const { recipient_id, reservation_id, notification_type, subject, content, priority = "medium" } = body;

		// Validate required fields
		if (!recipient_id || !notification_type || !subject || !content) {
			return NextResponse.json(
				{
					error: "Missing required fields: recipient_id, notification_type, subject, content",
				},
				{ status: 400 }
			);
		}

		// Validate notification type
		const validTypes = [
			"booking",
			"payment",
			"system",
			"message",
			"weather",
			"booking_confirmation",
			"reminder",
			"cancellation",
			"refund",
			"feedback_request",
			"promotional",
		];
		if (!validTypes.includes(notification_type)) {
			return NextResponse.json(
				{
					error: `Invalid notification_type. Must be one of: ${validTypes.join(", ")}`,
				},
				{ status: 400 }
			);
		}

		// Validate priority
		const validPriorities = ["low", "medium", "high"];
		if (!validPriorities.includes(priority)) {
			return NextResponse.json(
				{
					error: `Invalid priority. Must be one of: ${validPriorities.join(", ")}`,
				},
				{ status: 400 }
			);
		}

		const notificationData: NotificationInsert = {
			recipient_id,
			reservation_id,
			notification_type,
			subject,
			content,
			priority,
			status: "pending",
		};

		const { data: notification, error } = await supabaseAdmin
			.from("notifications")
			.insert(notificationData)
			.select(
				`
        *,
        recipient:profiles!recipient_id(first_name, last_name, email),
        reservation:reservations(id, qr_code)
      `
			)
			.single();

		if (error) {
			console.error("Error creating notification:", error);
			return NextResponse.json({ error: "Failed to create notification" }, { status: 500 });
		}

		// TODO: Add admin action logging if needed

		return NextResponse.json(
			{
				success: true,
				data: notification,
			},
			{ status: 201 }
		);
	} catch (error) {
		console.error("Notifications POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "notifications:write");

// PATCH /api/admin/notifications - Bulk operations (mark as read, delete multiple)
export const PATCH = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const body = await request.json();
		const { action, notification_ids } = body;

		if (!action || !notification_ids || !Array.isArray(notification_ids)) {
			return NextResponse.json(
				{
					error: "Missing required fields: action, notification_ids (array)",
				},
				{ status: 400 }
			);
		}

		let result;
		if (action === "mark_read") {
			const { data, error } = await supabaseAdmin
				.from("notifications")
				.update({
					is_read: true,
					read_at: new Date().toISOString(),
				})
				.in("id", notification_ids)
				.select();

			if (error) {
				console.error("Error marking notifications as read:", error);
				return NextResponse.json({ error: "Failed to mark notifications as read" }, { status: 500 });
			}

			result = data;
		} else if (action === "delete") {
			const { data, error } = await supabaseAdmin
				.from("notifications")
				.delete()
				.in("id", notification_ids)
				.select();

			if (error) {
				console.error("Error deleting notifications:", error);
				return NextResponse.json({ error: "Failed to delete notifications" }, { status: 500 });
			}

			result = data;
		} else {
			return NextResponse.json(
				{
					error: 'Invalid action. Must be "mark_read" or "delete"',
				},
				{ status: 400 }
			);
		}

		// TODO: Add admin action logging if needed

		return NextResponse.json({
			success: true,
			data: result,
			message: `Successfully ${action === "mark_read" ? "marked as read" : "deleted"} ${
				result?.length || 0
			} notifications`,
		});
	} catch (error) {
		console.error("Notifications PATCH error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "notifications:write");
