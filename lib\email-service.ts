import { Resend } from 'resend';
import { emailConfig } from './env';

// Initialize Resend client
let resend: Resend | null = null;

function getResendClient(): Resend {
  if (!resend && emailConfig.resend.apiKey) {
    resend = new Resend(emailConfig.resend.apiKey);
  }
  
  if (!resend) {
    throw new Error('Email service not configured. Please set RESEND_API_KEY environment variable.');
  }
  
  return resend;
}

export interface EmailData {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | Uint8Array;
    contentType?: string;
  }>;
}

/**
 * Send email using Resend
 */
export async function sendEmail(data: EmailData): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const client = getResendClient();
    
    const emailData = {
      from: emailConfig.resend.fromEmail,
      to: Array.isArray(data.to) ? data.to : [data.to],
      subject: data.subject,
      html: data.html,
      text: data.text,
      attachments: data.attachments,
    };

    const result = await client.emails.send(emailData);

    if (result.error) {
      console.error('Email sending error:', result.error);
      return {
        success: false,
        error: result.error.message || 'Failed to send email',
      };
    }

    return {
      success: true,
      messageId: result.data?.id,
    };
  } catch (error) {
    console.error('Email service error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown email error',
    };
  }
}

/**
 * Send booking confirmation email
 */
export async function sendBookingConfirmationEmail(
  customerEmail: string,
  customerName: string,
  bookingData: {
    reservationNumber: string;
    serviceName: string;
    date: string;
    time: string;
    participants: number;
    totalAmount: number;
    specialRequests?: string;
  },
  pdfAttachment?: Buffer
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  const subject = `Confirmation de réservation - ${bookingData.reservationNumber}`;
  
  const html = generateBookingConfirmationHTML(customerName, bookingData);
  const text = generateBookingConfirmationText(customerName, bookingData);

  const attachments = pdfAttachment ? [{
    filename: `confirmation-${bookingData.reservationNumber}.pdf`,
    content: pdfAttachment,
    contentType: 'application/pdf',
  }] : undefined;

  return sendEmail({
    to: customerEmail,
    subject,
    html,
    text,
    attachments,
  });
}

/**
 * Send booking reminder email
 */
export async function sendBookingReminderEmail(
  customerEmail: string,
  customerName: string,
  bookingData: {
    reservationNumber: string;
    serviceName: string;
    date: string;
    time: string;
    participants: number;
    totalAmount: number;
  }
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  const subject = `Rappel - Votre excursion demain - ${bookingData.reservationNumber}`;
  
  const html = generateBookingReminderHTML(customerName, bookingData);
  const text = generateBookingReminderText(customerName, bookingData);

  return sendEmail({
    to: customerEmail,
    subject,
    html,
    text,
  });
}

/**
 * Generate booking confirmation HTML email
 */
function generateBookingConfirmationHTML(customerName: string, bookingData: any): string {
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Confirmation de réservation</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .success-badge { background-color: #10b981; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="success-badge">✓ Réservation Confirmée</div>

      <h2>Bonjour ${customerName},</h2>
      
      <p>Nous avons le plaisir de confirmer votre réservation pour une excursion avec Soleil & Découverte !</p>

      <div class="info-section">
        <h3>Détails de votre réservation :</h3>
        <p><strong>Numéro de réservation :</strong> ${bookingData.reservationNumber}</p>
        <p><strong>Service :</strong> ${bookingData.serviceName}</p>
        <p><strong>Date :</strong> ${bookingData.date}</p>
        <p><strong>Heure :</strong> ${bookingData.time}</p>
        <p><strong>Participants :</strong> ${bookingData.participants}</p>
        <p><strong>Montant total :</strong> ${bookingData.totalAmount}€</p>
        ${bookingData.specialRequests ? `<p><strong>Demandes spéciales :</strong> ${bookingData.specialRequests}</p>` : ''}
      </div>

      <h3>Prochaines étapes :</h3>
      <ul>
        <li>Votre place est maintenant réservée</li>
        <li>Vous recevrez un rappel 24h avant votre excursion</li>
        <li>Rendez-vous au point de départ 15 minutes avant l'heure prévue</li>
        <li>Apportez une pièce d'identité</li>
      </ul>

      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate booking confirmation text email
 */
function generateBookingConfirmationText(customerName: string, bookingData: any): string {
  return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

✓ RÉSERVATION CONFIRMÉE

Bonjour ${customerName},

Nous avons le plaisir de confirmer votre réservation pour une excursion avec Soleil & Découverte !

DÉTAILS DE VOTRE RÉSERVATION :
- Numéro de réservation: ${bookingData.reservationNumber}
- Service: ${bookingData.serviceName}
- Date: ${bookingData.date}
- Heure: ${bookingData.time}
- Participants: ${bookingData.participants}
- Montant total: ${bookingData.totalAmount}€
${bookingData.specialRequests ? `- Demandes spéciales: ${bookingData.specialRequests}` : ''}

PROCHAINES ÉTAPES :
- Votre place est maintenant réservée
- Vous recevrez un rappel 24h avant votre excursion
- Rendez-vous au point de départ 15 minutes avant l'heure prévue
- Apportez une pièce d'identité

Merci pour votre confiance !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
}

/**
 * Generate booking reminder HTML email
 */
function generateBookingReminderHTML(customerName: string, bookingData: any): string {
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rappel - Votre excursion demain</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .reminder-badge { background-color: #f59e0b; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="reminder-badge">⏰ Rappel - Excursion Demain</div>

      <h2>Bonjour ${customerName},</h2>
      
      <p>Votre excursion avec Soleil & Découverte a lieu demain ! Nous avons hâte de vous accueillir.</p>

      <div class="info-section">
        <h3>Rappel de votre réservation :</h3>
        <p><strong>Numéro de réservation :</strong> ${bookingData.reservationNumber}</p>
        <p><strong>Service :</strong> ${bookingData.serviceName}</p>
        <p><strong>Date :</strong> ${bookingData.date}</p>
        <p><strong>Heure :</strong> ${bookingData.time}</p>
        <p><strong>Participants :</strong> ${bookingData.participants}</p>
      </div>

      <h3>Informations importantes :</h3>
      <ul>
        <li>Rendez-vous 15 minutes avant l'heure prévue</li>
        <li>Apportez une pièce d'identité</li>
        <li>Prévoyez des vêtements adaptés à l'activité</li>
        <li>N'hésitez pas à nous contacter pour toute question</li>
      </ul>

      <div class="footer">
        <p>Nous avons hâte de vous accueillir !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate booking reminder text email
 */
function generateBookingReminderText(customerName: string, bookingData: any): string {
  return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

⏰ RAPPEL - EXCURSION DEMAIN

Bonjour ${customerName},

Votre excursion avec Soleil & Découverte a lieu demain ! Nous avons hâte de vous accueillir.

RAPPEL DE VOTRE RÉSERVATION :
- Numéro de réservation: ${bookingData.reservationNumber}
- Service: ${bookingData.serviceName}
- Date: ${bookingData.date}
- Heure: ${bookingData.time}
- Participants: ${bookingData.participants}

INFORMATIONS IMPORTANTES :
- Rendez-vous 15 minutes avant l'heure prévue
- Apportez une pièce d'identité
- Prévoyez des vêtements adaptés à l'activité
- N'hésitez pas à nous contacter pour toute question

Nous avons hâte de vous accueillir !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
}
