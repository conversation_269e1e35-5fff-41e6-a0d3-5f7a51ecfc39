// Test script to verify role-based permission system
// Run with: node scripts/test-permissions.js

const { hasPermission, hasRoleLevel, getUserPermissions, PERMISSIONS } = require('../lib/permissions');

console.log('🧪 Testing Role-Based Permission System\n');

// Test users with different roles
const testUsers = [
  { role: 'admin', name: 'Admin User' },
  { role: 'manager', name: 'Manager User' },
  { role: 'employee', name: 'Employee User' },
  { role: null, name: 'Unauthenticated User' }
];

// Test permissions
const testPermissions = [
  'users:read',
  'users:write',
  'employees:read',
  'employees:write',
  'reservations:read',
  'reservations:write',
  'services:read',
  'services:write',
  'settings:read',
  'settings:write',
  'customers:read',
  'customers:write'
];

console.log('📋 Permission Matrix:');
console.log('='.repeat(80));

// Header
const header = 'Role'.padEnd(12) + testPermissions.map(p => p.split(':')[0].substring(0, 8)).join(' ').padEnd(60);
console.log(header);
console.log('-'.repeat(80));

// Test each user role
testUsers.forEach(user => {
  let row = (user.role || 'none').padEnd(12);
  
  testPermissions.forEach(permission => {
    const hasAccess = hasPermission(user.role, permission);
    row += (hasAccess ? '✓' : '✗').padEnd(9);
  });
  
  console.log(row);
});

console.log('\n📊 Role Hierarchy Test:');
console.log('='.repeat(40));

const roleTests = [
  { user: 'admin', required: 'employee', expected: true },
  { user: 'admin', required: 'manager', expected: true },
  { user: 'admin', required: 'admin', expected: true },
  { user: 'manager', required: 'employee', expected: true },
  { user: 'manager', required: 'manager', expected: true },
  { user: 'manager', required: 'admin', expected: false },
  { user: 'employee', required: 'employee', expected: true },
  { user: 'employee', required: 'manager', expected: false },
  { user: 'employee', required: 'admin', expected: false }
];

roleTests.forEach(test => {
  const result = hasRoleLevel(test.user, test.required);
  const status = result === test.expected ? '✅' : '❌';
  console.log(`${status} ${test.user} >= ${test.required}: ${result} (expected: ${test.expected})`);
});

console.log('\n🔑 Permissions by Role:');
console.log('='.repeat(40));

['admin', 'manager', 'employee'].forEach(role => {
  const permissions = getUserPermissions(role);
  console.log(`\n${role.toUpperCase()}:`);
  permissions.forEach(permission => {
    const description = PERMISSIONS[permission]?.description || 'No description';
    console.log(`  • ${permission}: ${description}`);
  });
});

console.log('\n✅ Permission system test completed!');
console.log('\nTest Users Created:');
console.log('• <EMAIL> (password: admin123) - Admin role');
console.log('• <EMAIL> (password: manager123) - Manager role');
console.log('• <EMAIL> (password: employee123) - Employee role');
console.log('\nYou can now test the admin interface with these users to verify:');
console.log('1. Navigation items are filtered based on permissions');
console.log('2. Settings link only shows for admin users');
console.log('3. User management is accessible to admin and manager');
console.log('4. API routes enforce permission checks');
