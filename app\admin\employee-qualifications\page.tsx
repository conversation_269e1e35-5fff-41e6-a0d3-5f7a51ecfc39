import AdminLayout from '@/components/admin/AdminLayout'
import EmployeeServiceQualifications from '@/components/admin/EmployeeServiceQualifications'
import { Card, CardContent } from '@/components/ui/card'
import { Award, Users } from 'lucide-react'

export default function EmployeeQualificationsPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Award className="w-6 h-6" />
            Qualifications des employés
          </h1>
          <p className="text-gray-600 mt-1">
            G<PERSON>rez les assignations de services aux employés pour le système de disponibilité dynamique
          </p>
        </div>

        {/* Info Card */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Users className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900">Système de disponibilité dynamique</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Les qualifications déterminent quels employés peuvent être assignés à quels services.
                  Seuls les employés qualifiés et disponibles apparaîtront dans les créneaux de réservation.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Component */}
        <EmployeeServiceQualifications />
      </div>
    </AdminLayout>
  )
}
