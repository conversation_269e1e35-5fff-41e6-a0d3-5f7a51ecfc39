import { supabaseAdmin } from './supabase'
import { Notification, NotificationInsert, UINotification } from './types'

// Type mapping between database and UI notification types
const TYPE_MAPPING: Record<string, string> = {
  'booking_confirmation': 'booking',
  'reminder': 'booking',
  'cancellation': 'booking',
  'refund': 'payment',
  'feedback_request': 'message',
  'promotional': 'message',
  'booking': 'booking',
  'payment': 'payment',
  'system': 'system',
  'message': 'message',
  'weather': 'weather'
}

// Convert database notification to UI notification
export function mapNotificationToUI(dbNotification: Notification): UINotification {
  return {
    id: dbNotification.id,
    type: TYPE_MAPPING[dbNotification.notification_type] as UINotification['type'] || 'system',
    title: dbNotification.subject,
    message: dbNotification.content,
    timestamp: dbNotification.created_at || new Date().toISOString(),
    read: dbNotification.is_read || false,
    priority: (dbNotification.priority as UINotification['priority']) || 'medium',
    reservation_id: dbNotification.reservation_id || undefined
  }
}

// Convert UI notification to database notification
export function mapUINotificationToDb(uiNotification: Partial<UINotification>, recipient_id: string): NotificationInsert {
  // Map UI type back to database type (use the first matching database type)
  const dbType = Object.entries(TYPE_MAPPING).find(([dbType, uiType]) => uiType === uiNotification.type)?.[0] || uiNotification.type || 'system'
  
  return {
    recipient_id,
    reservation_id: uiNotification.reservation_id,
    notification_type: dbType,
    subject: uiNotification.title || '',
    content: uiNotification.message || '',
    priority: uiNotification.priority || 'medium',
    is_read: uiNotification.read || false,
    status: 'pending'
  }
}

// Notification creation helpers for different event types
export interface NotificationTemplate {
  type: string
  subject: string
  content: string
  priority: 'low' | 'medium' | 'high'
}

export function createBookingConfirmationNotification(
  customerName: string, 
  serviceName: string, 
  date: string, 
  reservationId: string
): NotificationTemplate {
  return {
    type: 'booking_confirmation',
    subject: 'Nouvelle réservation confirmée',
    content: `${customerName} a confirmé sa réservation pour "${serviceName}" le ${date}`,
    priority: 'medium'
  }
}

export function createPaymentReceivedNotification(
  amount: number, 
  currency: string, 
  reservationId: string
): NotificationTemplate {
  return {
    type: 'payment',
    subject: 'Paiement reçu',
    content: `Paiement de ${amount}${currency} reçu pour la réservation #${reservationId}`,
    priority: 'high'
  }
}

export function createCancellationNotification(
  customerName: string, 
  serviceName: string, 
  date: string, 
  reason?: string
): NotificationTemplate {
  return {
    type: 'cancellation',
    subject: 'Réservation annulée',
    content: `${customerName} a annulé sa réservation pour "${serviceName}" le ${date}${reason ? `. Raison: ${reason}` : ''}`,
    priority: 'medium'
  }
}

export function createSystemMaintenanceNotification(
  startTime: string, 
  endTime: string, 
  description?: string
): NotificationTemplate {
  return {
    type: 'system',
    subject: 'Maintenance programmée',
    content: `Maintenance du système prévue de ${startTime} à ${endTime}${description ? `. ${description}` : ''}`,
    priority: 'low'
  }
}

export function createWeatherAlertNotification(
  condition: string, 
  date: string, 
  impact?: string
): NotificationTemplate {
  return {
    type: 'weather',
    subject: 'Alerte météo',
    content: `Conditions météorologiques: ${condition} prévues pour ${date}${impact ? `. Impact: ${impact}` : ''}`,
    priority: 'high'
  }
}

export function createCustomerMessageNotification(
  customerName: string, 
  subject: string, 
  reservationId?: string
): NotificationTemplate {
  return {
    type: 'message',
    subject: 'Message client',
    content: `${customerName} a envoyé un message${subject ? ` concernant: ${subject}` : ''}${reservationId ? ` (Réservation #${reservationId})` : ''}`,
    priority: 'medium'
  }
}

// Utility function to create and save notification
export async function createNotification(
  recipientId: string,
  template: NotificationTemplate,
  reservationId?: string
): Promise<Notification | null> {
  try {
    const notificationData: NotificationInsert = {
      recipient_id: recipientId,
      reservation_id: reservationId,
      notification_type: template.type,
      subject: template.subject,
      content: template.content,
      priority: template.priority,
      status: 'pending'
    }

    const { data, error } = await supabaseAdmin
      .from('notifications')
      .insert(notificationData)
      .select()
      .single()

    if (error) {
      console.error('Error creating notification:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in createNotification:', error)
    return null
  }
}

// Utility function to mark notification as read
export async function markNotificationAsRead(notificationId: string): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('notifications')
      .update({ 
        is_read: true, 
        read_at: new Date().toISOString() 
      })
      .eq('id', notificationId)

    if (error) {
      console.error('Error marking notification as read:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in markNotificationAsRead:', error)
    return false
  }
}

// Utility function to get unread notification count for a user
export async function getUnreadNotificationCount(recipientId: string): Promise<number> {
  try {
    const { count, error } = await supabaseAdmin
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('recipient_id', recipientId)
      .eq('is_read', false)

    if (error) {
      console.error('Error getting unread notification count:', error)
      return 0
    }

    return count || 0
  } catch (error) {
    console.error('Error in getUnreadNotificationCount:', error)
    return 0
  }
}

// Utility function to delete old notifications (cleanup)
export async function deleteOldNotifications(daysOld: number = 90): Promise<number> {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    const { data, error } = await supabaseAdmin
      .from('notifications')
      .delete()
      .lt('created_at', cutoffDate.toISOString())
      .select('id')

    if (error) {
      console.error('Error deleting old notifications:', error)
      return 0
    }

    return data?.length || 0
  } catch (error) {
    console.error('Error in deleteOldNotifications:', error)
    return 0
  }
}
