import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Service = Database["public"]["Tables"]["services"]["Row"];
type ServiceInsert = Database["public"]["Tables"]["services"]["Insert"];
type ServiceUpdate = Database["public"]["Tables"]["services"]["Update"];

// GET /api/admin/services - List all services with admin details
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const search = searchParams.get("search");
		const category = searchParams.get("category");
		const isActive = searchParams.get("active");
		const offset = (page - 1) * limit;

		// Build query
		let query = supabaseAdmin
			.from("services")
			.select(
				`
        *,
        pricing_tiers (
          id,
          tier_name,
          price,
          min_age,
          max_age,
          is_active
        ),
        service_equipment_requirements (
          id,
          capacity_per_participant,
          equipment (
            id,
            name,
            total_capacity
          )
        ),
        service_scheduling_rules (
          id,
          days_of_week,
          operating_start_time,
          operating_end_time,
          booking_interval_minutes,
          specific_times,
          max_bookings_per_day,
          is_active
        ),
        service_blackout_dates (
          id,
          start_date,
          end_date,
          reason,
          is_active
        )
      `
			)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (search) {
			query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
		}
		if (category && category !== "all") {
			query = query.eq("category", category);
		}
		if (isActive !== null) {
			query = query.eq("is_active", isActive === "true");
		}

		const { data: services, error, count } = await query;

		if (error) {
			console.error("Error fetching services:", error);
			return NextResponse.json({ error: "Failed to fetch services" }, { status: 500 });
		}

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin.from("services").select("*", { count: "exact", head: true });

		return NextResponse.json({
			services: services || [],
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Services GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// POST /api/admin/services - Create new service
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const serviceData: ServiceInsert = await request.json();

		// Validate required fields
		if (
			!serviceData.name ||
			!serviceData.duration_minutes ||
			!serviceData.base_price ||
			!serviceData.max_participants
		) {
			return NextResponse.json(
				{ error: "Missing required fields: name, duration_minutes, base_price, max_participants" },
				{ status: 400 }
			);
		}

		// Create service
		const { data: service, error } = await supabaseAdmin.from("services").insert(serviceData).select().single();

		if (error) {
			console.error("Error creating service:", error);
			return NextResponse.json({ error: "Failed to create service" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "CREATE", "services", service.id, null, service, request);

		return NextResponse.json({ service }, { status: 201 });
	} catch (error) {
		console.error("Service POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// PUT /api/admin/services - Bulk update services
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { serviceIds, updates }: { serviceIds: string[]; updates: ServiceUpdate } = await request.json();

		if (!serviceIds || serviceIds.length === 0) {
			return NextResponse.json({ error: "No service IDs provided" }, { status: 400 });
		}

		// Get current services for audit log
		const { data: currentServices } = await supabaseAdmin.from("services").select("*").in("id", serviceIds);

		// Update services
		const { data: updatedServices, error } = await supabaseAdmin
			.from("services")
			.update(updates)
			.in("id", serviceIds)
			.select();

		if (error) {
			console.error("Error updating services:", error);
			return NextResponse.json({ error: "Failed to update services" }, { status: 500 });
		}

		// Log admin actions
		for (const service of updatedServices || []) {
			const oldService = currentServices?.find((s) => s.id === service.id);
			await logAdminAction(user.id, "UPDATE", "services", service.id, oldService, service, request);
		}

		return NextResponse.json({
			services: updatedServices,
			updated: updatedServices?.length || 0,
		});
	} catch (error) {
		console.error("Services PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/services - Bulk delete services
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { serviceIds }: { serviceIds: string[] } = await request.json();

		if (!serviceIds || serviceIds.length === 0) {
			return NextResponse.json({ error: "No service IDs provided" }, { status: 400 });
		}

		// Get services for audit log before deletion
		const { data: servicesToDelete } = await supabaseAdmin.from("services").select("*").in("id", serviceIds);

		// Check for existing reservations
		const { data: existingReservations } = await supabaseAdmin
			.from("reservations")
			.select("id, service_id")
			.in("service_id", serviceIds)
			.gte("start_time", new Date().toISOString());

		if (existingReservations && existingReservations.length > 0) {
			return NextResponse.json({ error: "Cannot delete services with future reservations" }, { status: 400 });
		}

		// Start cascade deletion for each service
		for (const serviceId of serviceIds) {
			// 1. Delete service scheduling rules
			await supabaseAdmin.from("service_scheduling_rules").delete().eq("service_id", serviceId);

			// 2. Delete service blackout dates
			await supabaseAdmin.from("service_blackout_dates").delete().eq("service_id", serviceId);

			// 3. Delete service equipment requirements
			await supabaseAdmin.from("service_equipment_requirements").delete().eq("service_id", serviceId);

			// 4. Delete employee service qualifications
			await supabaseAdmin.from("employee_service_qualifications").delete().eq("service_id", serviceId);

			// 5. Delete pricing tiers
			await supabaseAdmin.from("pricing_tiers").delete().eq("service_id", serviceId);

			// 6. Update any employees that have this as default service
			await supabaseAdmin
				.from("employees")
				.update({ default_service_id: null })
				.eq("default_service_id", serviceId);

			// 7. Handle completed/cancelled reservations - keep for historical data but mark service as deleted
			const serviceToDelete = servicesToDelete?.find((s) => s.id === serviceId);
			if (serviceToDelete) {
				const { data: historicalReservations } = await supabaseAdmin
					.from("reservations")
					.select("id")
					.eq("service_id", serviceId)
					.in("status", ["completed", "cancelled", "no_show"]);

				if (historicalReservations && historicalReservations.length > 0) {
					// Add a note to historical reservations that the service was deleted
					await supabaseAdmin
						.from("reservations")
						.update({
							admin_notes: `Service "${
								serviceToDelete.name
							}" was deleted on ${new Date().toISOString()}. ${serviceToDelete.admin_notes || ""}`.trim(),
						})
						.eq("service_id", serviceId)
						.in("status", ["completed", "cancelled", "no_show"]);
				}
			}
		}

		// Finally, delete all services
		const { error } = await supabaseAdmin.from("services").delete().in("id", serviceIds);

		if (error) {
			console.error("Error deleting services:", error);
			return NextResponse.json({ error: "Failed to delete services" }, { status: 500 });
		}

		// Log admin actions
		for (const service of servicesToDelete || []) {
			await logAdminAction(user.id, "DELETE", "services", service.id, service, null, request);
		}

		return NextResponse.json({
			deleted: serviceIds.length,
			message: "Services and all related data deleted successfully",
		});
	} catch (error) {
		console.error("Services DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");
