"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AvailabilityResponse, TimeSlotInfo, TimeSlotWithAvailability } from "@/lib/types";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { AlertCircle, Clock, Loader2, Users } from "lucide-react";
import { useEffect, useState } from "react";

interface AvailabilityCalendarProps {
	serviceId: string;
	participantCount: number;
	onTimeSlotSelect: (timeSlot: TimeSlotInfo, date: Date) => void;
	selectedDate?: Date;
	selectedTimeSlot?: TimeSlotInfo;
}

export default function AvailabilityCalendar({
	serviceId,
	participantCount,
	onTimeSlotSelect,
	selectedDate,
	selectedTimeSlot,
}: AvailabilityCalendarProps) {
	const [date, setDate] = useState<Date | undefined>(selectedDate);
	const [availability, setAvailability] = useState<AvailabilityResponse | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Fetch availability when date or participant count changes
	useEffect(() => {
		if (date && serviceId) {
			fetchAvailability();
		}
	}, [date, serviceId, participantCount]);

	const fetchAvailability = async () => {
		if (!date) return;

		setLoading(true);
		setError(null);

		try {
			const dateStr = format(date, "yyyy-MM-dd");
			const response = await fetch(
				`/api/services/${serviceId}/availability?date=${dateStr}&participants=${participantCount}`
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to fetch availability");
			}

			const data = await response.json();
			setAvailability(data.data);
		} catch (err) {
			console.error("Error fetching availability:", err);
			setError(err instanceof Error ? err.message : "Failed to load availability");
			setAvailability(null);
		} finally {
			setLoading(false);
		}
	};

	const handleDateSelect = (newDate: Date | undefined) => {
		setDate(newDate);
		setAvailability(null);
	};

	const handleTimeSlotSelect = (timeSlot: TimeSlotWithAvailability) => {
		if (date && timeSlot.is_available) {
			onTimeSlotSelect(timeSlot, date);
		}
	};

	const formatTime = (dateTimeString: string) => {
		return format(new Date(dateTimeString), "HH:mm", { locale: fr });
	};

	const getTimeSlotStatus = (timeSlot: TimeSlotWithAvailability) => {
		if (!timeSlot.is_available) {
			return { variant: "destructive" as const, text: "Complet" };
		}
		if (timeSlot.available_capacity <= 3) {
			return { variant: "secondary" as const, text: `${timeSlot.available_capacity} places` };
		}
		return { variant: "default" as const, text: "Disponible" };
	};

	return (
		<div className="space-y-6">
			{/* Calendar */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Clock className="w-5 h-5" />
						Sélectionnez une date
					</CardTitle>
				</CardHeader>
				<CardContent className="flex justify-center">
					<Calendar
						mode="single"
						selected={date}
						onSelect={handleDateSelect}
						initialFocus
						fromDate={new Date()}
						toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
						disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
						className="rounded-md border"
					/>
				</CardContent>
			</Card>

			{/* Time Slots */}
			{date && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="w-5 h-5" />
							Créneaux disponibles - {format(date, "EEEE d MMMM yyyy", { locale: fr })}
						</CardTitle>
						{participantCount > 1 && (
							<p className="text-sm text-muted-foreground">
								Pour {participantCount} participant{participantCount > 1 ? "s" : ""}
							</p>
						)}
					</CardHeader>
					<CardContent>
						{loading && (
							<div className="flex items-center justify-center py-8">
								<Loader2 className="w-6 h-6 animate-spin text-emerald-600" />
								<span className="ml-2 text-emerald-600">Chargement des créneaux...</span>
							</div>
						)}

						{error && (
							<div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg">
								<AlertCircle className="w-5 h-5 text-red-600" />
								<span className="text-red-700">{error}</span>
							</div>
						)}

						{availability && !loading && !error && (
							<div className="space-y-3">
								{availability.timeSlots.length === 0 ? (
									<div className="text-center py-8 text-muted-foreground">
										<Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
										<p>Aucun créneau disponible pour cette date</p>
										<p className="text-sm">Essayez une autre date</p>
									</div>
								) : (
									<div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
										{availability.timeSlots.map((timeSlot) => {
											const status = getTimeSlotStatus({ ...timeSlot, service_id: serviceId });
											const isSelected = selectedTimeSlot?.id === timeSlot.id;

											return (
												<Button
													key={timeSlot.id}
													variant={isSelected ? "default" : "outline"}
													className={`h-auto p-4 flex flex-col items-start gap-2 ${
														!timeSlot.is_available ? "opacity-50 cursor-not-allowed" : ""
													} ${isSelected ? "ring-2 ring-emerald-500" : ""}`}
													onClick={() =>
														handleTimeSlotSelect({ ...timeSlot, service_id: serviceId })
													}
													disabled={!timeSlot.is_available}
												>
													<div className="flex items-center justify-between w-full">
														<span className="font-medium">
															{formatTime(timeSlot.start_time)} -{" "}
															{formatTime(timeSlot.end_time)}
														</span>
														<Badge variant={status.variant} className="text-xs">
															{status.text}
														</Badge>
													</div>
													{timeSlot.is_available && (
														<div className="text-xs text-muted-foreground">
															{timeSlot.available_capacity} place
															{timeSlot.available_capacity > 1 ? "s" : ""} disponible
															{timeSlot.available_capacity > 1 ? "s" : ""}
														</div>
													)}
												</Button>
											);
										})}
									</div>
								)}
							</div>
						)}
					</CardContent>
				</Card>
			)}
		</div>
	);
}
