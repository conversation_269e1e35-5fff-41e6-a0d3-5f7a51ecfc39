import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// GET /api/admin/employee-service-qualifications - Get all qualifications
export const GET = withAdminAuth(async (request: NextRequest, user) => {
	try {
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: qualifications, error } = await supabaseAdmin
			.from("employee_service_qualifications")
			.select(
				`
        *,
        employee:employees(id, first_name, last_name, email),
        service:services(id, name)
      `
			)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Error fetching qualifications:", error);
			return NextResponse.json({ error: "Failed to fetch qualifications" }, { status: 500 });
		}

		return NextResponse.json({ qualifications });
	} catch (error) {
		console.error("Qualifications GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "employees:read");

// POST /api/admin/employee-service-qualifications - Create new qualification
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const qualificationData = await request.json();

		// Validate required fields
		if (!qualificationData.employee_id || !qualificationData.service_id) {
			return NextResponse.json({ error: "Employee ID and Service ID are required" }, { status: 400 });
		}

		// Check if qualification already exists
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: existing } = await supabaseAdmin
			.from("employee_service_qualifications")
			.select("id")
			.eq("employee_id", qualificationData.employee_id)
			.eq("service_id", qualificationData.service_id)
			.single();

		if (existing) {
			return NextResponse.json({ error: "This qualification already exists" }, { status: 400 });
		}

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: qualification, error } = await supabaseAdmin
			.from("employee_service_qualifications")
			.insert(qualificationData)
			.select()
			.single();

		if (error) {
			console.error("Error creating qualification:", error);
			return NextResponse.json({ error: "Failed to create qualification" }, { status: 500 });
		}

		return NextResponse.json({ qualification }, { status: 201 });
	} catch (error) {
		console.error("Qualifications POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "employees:write");
