// Environment variables validation and configuration

function getEnvVar(name: string, defaultValue?: string): string {
	const value = process.env[name] || defaultValue;

	if (!value) {
		// Only throw on server side to prevent client-side crashes
		if (typeof window === "undefined") {
			throw new Error(`Missing required environment variable: ${name}`);
		} else {
			console.warn(`Missing environment variable: ${name}`);
			return "";
		}
	}

	return value;
}

function getOptionalEnvVar(name: string, defaultValue?: string): string | undefined {
	return process.env[name] || defaultValue;
}

// Supabase configuration
export const supabaseConfig = {
	url: getEnvVar("NEXT_PUBLIC_SUPABASE_URL"),
	anonKey: getEnvVar("NEXT_PUBLIC_SUPABASE_ANON_KEY"),
	// Service role key is only available on server side
	serviceRoleKey: typeof window === "undefined" ? getEnvVar("SUPABASE_SERVICE_ROLE_KEY") : "",
} as const;

// Application configuration
export const appConfig = {
	url: getEnvVar("NEXT_PUBLIC_APP_URL", "http://localhost:3000"),
	nodeEnv: getEnvVar("NODE_ENV", "development"),
	isDevelopment: process.env.NODE_ENV === "development",
	isProduction: process.env.NODE_ENV === "production",
} as const;

// Business configuration is now stored in database (business_settings table)
// Use useSettings() hook or getSettings() function to access business config

// Payment configuration
export const paymentConfig = {
	stripe: {
		publishableKey: getOptionalEnvVar("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),
		secretKey: getOptionalEnvVar("STRIPE_SECRET_KEY"),
		webhookSecret: getOptionalEnvVar("STRIPE_WEBHOOK_SECRET"),
	},
} as const;

// Email configuration
export const emailConfig = {
	resend: {
		apiKey: getOptionalEnvVar("RESEND_API_KEY"),
		fromEmail: getOptionalEnvVar("RESEND_FROM_EMAIL", "<EMAIL>"),
	},
} as const;

// Debug environment variables
if (typeof window !== "undefined") {
	console.log("=== CLIENT ENV DEBUG ===");
	console.log("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY from process.env:", process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
	console.log("paymentConfig.stripe.publishableKey:", paymentConfig.stripe.publishableKey);
}

// Validate Stripe configuration
export const validateStripeConfig = () => {
	const errors: string[] = [];

	if (!paymentConfig.stripe.publishableKey) {
		errors.push("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required");
	}

	if (typeof window === "undefined" && !paymentConfig.stripe.secretKey) {
		errors.push("STRIPE_SECRET_KEY is required for server-side operations");
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
};

// Email and debug configuration moved to database settings
// Use useSettings() hook to access these configurations

// Validate critical environment variables on startup
export function validateEnvironment() {
	try {
		const environment = typeof window === "undefined" ? "server" : "client";

		// Validate Supabase configuration
		if (!supabaseConfig.url || !supabaseConfig.anonKey) {
			const message = `Missing Supabase configuration on ${environment}`;
			if (typeof window === "undefined") {
				throw new Error(message);
			} else {
				console.warn(message);
				return false;
			}
		}

		// Only validate service role key on server side
		if (typeof window === "undefined" && !supabaseConfig.serviceRoleKey) {
			throw new Error("Missing Supabase service role key on server");
		}

		console.log(`✅ Environment variables validated successfully (${environment})`);
		console.log("🌍 Environment:", appConfig.nodeEnv);

		return true;
	} catch (error) {
		console.error("❌ Environment validation failed:", error);
		throw error;
	}
}

// Call validation on module load
// Note: This will run on both server and client, but validation is environment-aware
try {
	validateEnvironment();
} catch (error) {
	// Only throw on server side to prevent client-side crashes
	if (typeof window === "undefined") {
		throw error;
	} else {
		console.warn("Environment validation failed on client side:", error);
	}
}
