"use client";

import { ServiceOption } from "@/lib/types/services";
import { useState, useEffect } from "react";

interface ServiceOptionsSelectorProps {
	options: ServiceOption[];
	selectedOptions: string[];
	onSelectionChange: (selectedOptions: string[]) => void;
	onPriceChange: (totalOptionsPrice: number) => void;
}

export default function ServiceOptionsSelector({
	options,
	selectedOptions,
	onSelectionChange,
	onPriceChange,
}: ServiceOptionsSelectorProps) {
	const [selections, setSelections] = useState<string[]>(selectedOptions);
	const [errors, setErrors] = useState<string[]>([]);

	// Calculate total options price
	const calculateOptionsPrice = (selected: string[]) => {
		return options
			.filter(option => selected.includes(option.id))
			.reduce((total, option) => total + option.price, 0);
	};

	// Validate option selections based on rules
	const validateSelections = (selected: string[]) => {
		const validationErrors: string[] = [];

		// Group options by type for validation
		const optionsByType = options.reduce((acc, option) => {
			if (!acc[option.type]) acc[option.type] = [];
			acc[option.type].push(option);
			return acc;
		}, {} as Record<string, ServiceOption[]>);

		// Check single choice constraints
		const singleChoiceOptions = optionsByType.single_choice || [];
		const singleChoiceRequiredOptions = optionsByType.single_choice_required || [];
		
		// For single choice groups, only one option can be selected per group
		// Note: In a real implementation, you might want to group options by a group_id
		
		// Check required minimum selections
		const requiredMinOptions = optionsByType.required_min || [];
		for (const option of requiredMinOptions) {
			const selectedFromGroup = selected.filter(id => id === option.id);
			if (selectedFromGroup.length < (option.min_selections || 1)) {
				validationErrors.push(`${option.name} nécessite au minimum ${option.min_selections || 1} sélection(s)`);
			}
		}

		// Check single choice required
		const requiredSingleChoice = singleChoiceRequiredOptions.filter(option => option.required);
		if (requiredSingleChoice.length > 0) {
			const hasRequiredSelection = requiredSingleChoice.some(option => selected.includes(option.id));
			if (!hasRequiredSelection) {
				validationErrors.push("Vous devez sélectionner au moins une option obligatoire");
			}
		}

		return validationErrors;
	};

	const handleOptionToggle = (optionId: string, option: ServiceOption) => {
		let newSelections = [...selections];

		if (option.type === "single_choice" || option.type === "single_choice_required") {
			// For single choice, deselect others of the same type and select this one
			const otherSingleChoiceOptions = options
				.filter(opt => (opt.type === "single_choice" || opt.type === "single_choice_required") && opt.id !== optionId)
				.map(opt => opt.id);
			
			newSelections = newSelections.filter(id => !otherSingleChoiceOptions.includes(id));
			
			if (!selections.includes(optionId)) {
				newSelections.push(optionId);
			}
		} else {
			// For other types, toggle selection
			if (selections.includes(optionId)) {
				newSelections = newSelections.filter(id => id !== optionId);
			} else {
				newSelections.push(optionId);
			}
		}

		setSelections(newSelections);
		
		// Validate and update parent
		const validationErrors = validateSelections(newSelections);
		setErrors(validationErrors);
		
		onSelectionChange(newSelections);
		onPriceChange(calculateOptionsPrice(newSelections));
	};

	// Update when props change
	useEffect(() => {
		setSelections(selectedOptions);
	}, [selectedOptions]);

	if (!options || options.length === 0) {
		return null;
	}

	return (
		<div className="space-y-4">
			<h3 className="text-lg font-semibold text-gray-900">Options du service</h3>
			
			{errors.length > 0 && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-3">
					<ul className="text-sm text-red-700 list-disc list-inside">
						{errors.map((error, index) => (
							<li key={index}>{error}</li>
						))}
					</ul>
				</div>
			)}

			<div className="space-y-3">
				{options.map((option) => {
					const isSelected = selections.includes(option.id);
					const isRequired = option.type === "single_choice_required" && option.required;
					
					return (
						<div
							key={option.id}
							className={`border rounded-lg p-4 cursor-pointer transition-colors ${
								isSelected
									? "border-emerald-500 bg-emerald-50"
									: "border-gray-200 hover:border-gray-300"
							}`}
							onClick={() => handleOptionToggle(option.id, option)}
						>
							<div className="flex items-start justify-between">
								<div className="flex items-start space-x-3">
									<input
										type={option.type === "single_choice" || option.type === "single_choice_required" ? "radio" : "checkbox"}
										checked={isSelected}
										onChange={() => {}} // Handled by div click
										className="mt-1 text-emerald-600 focus:ring-emerald-500"
									/>
									<div className="flex-1">
										<div className="flex items-center gap-2">
											<h4 className="font-medium text-gray-900">{option.name}</h4>
											{isRequired && (
												<span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
													Obligatoire
												</span>
											)}
										</div>
										{option.description && (
											<p className="text-sm text-gray-600 mt-1">{option.description}</p>
										)}
										{option.type === "required_min" && option.min_selections && (
											<p className="text-xs text-gray-500 mt-1">
												Minimum {option.min_selections} sélection(s) requise(s)
											</p>
										)}
									</div>
								</div>
								<div className="text-right">
									<span className="font-semibold text-gray-900">
										{option.price > 0 ? `+${option.price.toFixed(2)} €` : "Gratuit"}
									</span>
								</div>
							</div>
						</div>
					);
				})}
			</div>

			{selections.length > 0 && (
				<div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
					<div className="flex justify-between items-center">
						<span className="text-sm font-medium text-emerald-800">
							Total options sélectionnées:
						</span>
						<span className="font-semibold text-emerald-900">
							+{calculateOptionsPrice(selections).toFixed(2)} €
						</span>
					</div>
				</div>
			)}
		</div>
	);
}
