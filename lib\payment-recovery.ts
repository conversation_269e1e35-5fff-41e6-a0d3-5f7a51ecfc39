import { supabase } from './supabase';
import { createPaymentIntent, eurosToCents } from './stripe';

export interface PaymentRetryOptions {
  reservationId: string;
  maxRetries?: number;
  retryDelay?: number; // in milliseconds
}

export interface PaymentRecoveryResult {
  success: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  error?: string;
  retryCount?: number;
  canRetry?: boolean;
}

// Get user-friendly error messages for common Stripe errors
export const getPaymentErrorMessage = (error: any): string => {
  if (!error) return 'Une erreur inconnue s\'est produite';

  const errorCode = error.code || error.decline_code || error.type;
  const errorMessage = error.message || '';

  // Common Stripe error codes and user-friendly messages
  const errorMessages: Record<string, string> = {
    // Card errors
    'card_declined': 'Votre carte a été refusée. Veuillez essayer avec une autre carte.',
    'expired_card': 'Votre carte a expiré. Veuillez utiliser une carte valide.',
    'incorrect_cvc': 'Le code de sécurité (CVC) est incorrect.',
    'incorrect_number': 'Le numéro de carte est incorrect.',
    'invalid_expiry_month': 'Le mois d\'expiration est invalide.',
    'invalid_expiry_year': 'L\'année d\'expiration est invalide.',
    'invalid_number': 'Le numéro de carte est invalide.',
    'processing_error': 'Une erreur s\'est produite lors du traitement. Veuillez réessayer.',
    'insufficient_funds': 'Fonds insuffisants sur votre carte.',
    
    // Decline codes
    'generic_decline': 'Votre carte a été refusée. Contactez votre banque pour plus d\'informations.',
    'lost_card': 'Votre carte a été signalée comme perdue. Utilisez une autre carte.',
    'stolen_card': 'Votre carte a été signalée comme volée. Utilisez une autre carte.',
    'pickup_card': 'Votre carte a été refusée. Contactez votre banque.',
    'restricted_card': 'Votre carte a des restrictions. Contactez votre banque.',
    'security_violation': 'Transaction refusée pour des raisons de sécurité.',
    'service_not_allowed': 'Ce type de transaction n\'est pas autorisé sur votre carte.',
    'transaction_not_allowed': 'Cette transaction n\'est pas autorisée.',
    
    // Authentication errors
    'authentication_required': 'Authentification requise. Veuillez suivre les instructions de votre banque.',
    'approve_with_id': 'Veuillez contacter votre banque pour approuver cette transaction.',
    
    // Network/processing errors
    'issuer_not_available': 'Votre banque n\'est pas disponible. Veuillez réessayer plus tard.',
    'try_again_later': 'Erreur temporaire. Veuillez réessayer dans quelques minutes.',
    
    // API errors
    'api_connection_error': 'Problème de connexion. Vérifiez votre connexion internet.',
    'api_error': 'Erreur du service de paiement. Veuillez réessayer.',
    'rate_limit_error': 'Trop de tentatives. Veuillez attendre avant de réessayer.',
  };

  // Check for specific error codes first
  if (errorCode && errorMessages[errorCode]) {
    return errorMessages[errorCode];
  }

  // Check for common error message patterns
  if (errorMessage.toLowerCase().includes('insufficient funds')) {
    return 'Fonds insuffisants sur votre carte.';
  }
  if (errorMessage.toLowerCase().includes('expired')) {
    return 'Votre carte a expiré. Veuillez utiliser une carte valide.';
  }
  if (errorMessage.toLowerCase().includes('declined')) {
    return 'Votre carte a été refusée. Veuillez essayer avec une autre carte.';
  }
  if (errorMessage.toLowerCase().includes('authentication')) {
    return 'Authentification requise. Veuillez suivre les instructions de votre banque.';
  }

  // Default message with original error for debugging
  return `Erreur de paiement: ${errorMessage}`;
};

// Check if a payment error is retryable
export const isRetryableError = (error: any): boolean => {
  if (!error) return false;

  const errorCode = error.code || error.decline_code || error.type;
  const errorMessage = error.message || '';

  // Non-retryable errors (permanent failures)
  const nonRetryableErrors = [
    'card_declined',
    'expired_card',
    'incorrect_cvc',
    'incorrect_number',
    'invalid_expiry_month',
    'invalid_expiry_year',
    'invalid_number',
    'insufficient_funds',
    'lost_card',
    'stolen_card',
    'restricted_card',
    'service_not_allowed',
    'transaction_not_allowed',
  ];

  if (errorCode && nonRetryableErrors.includes(errorCode)) {
    return false;
  }

  // Retryable errors (temporary failures)
  const retryableErrors = [
    'processing_error',
    'issuer_not_available',
    'try_again_later',
    'api_connection_error',
    'api_error',
    'rate_limit_error',
  ];

  if (errorCode && retryableErrors.includes(errorCode)) {
    return true;
  }

  // Check message patterns for retryable errors
  if (errorMessage.toLowerCase().includes('temporary') ||
      errorMessage.toLowerCase().includes('try again') ||
      errorMessage.toLowerCase().includes('connection')) {
    return true;
  }

  // Default to non-retryable for unknown errors
  return false;
};

// Create a new payment intent for retry
export const retryPayment = async (options: PaymentRetryOptions): Promise<PaymentRecoveryResult> => {
  try {
    const { reservationId, maxRetries = 3 } = options;

    // Get reservation details
    const { data: reservation, error: reservationError } = await supabase
      .from('reservations')
      .select(`
        *,
        customer:customers(first_name, last_name, email),
        service:services(name)
      `)
      .eq('id', reservationId)
      .single();

    if (reservationError || !reservation) {
      return {
        success: false,
        error: 'Réservation non trouvée',
        canRetry: false,
      };
    }

    // Check how many payment attempts have been made
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('reservation_id', reservationId)
      .order('created_at', { ascending: false });

    if (paymentsError) {
      return {
        success: false,
        error: 'Erreur lors de la vérification des paiements',
        canRetry: true,
      };
    }

    const retryCount = payments?.length || 0;

    if (retryCount >= maxRetries) {
      return {
        success: false,
        error: `Nombre maximum de tentatives atteint (${maxRetries})`,
        canRetry: false,
        retryCount,
      };
    }

    // Create new payment intent
    const amountInCents = eurosToCents(reservation.total_amount);
    const customerName = reservation.customer 
      ? `${reservation.customer.first_name} ${reservation.customer.last_name}`
      : 'Unknown Customer';

    const result = await createPaymentIntent({
      amount: amountInCents,
      currency: 'eur',
      reservationId,
      customerEmail: reservation.customer?.email,
      customerName,
      description: `Retry payment for ${reservation.service?.name || 'service'} - Reservation #${reservation.reservation_number}`,
    });

    if (!result.success) {
      return {
        success: false,
        error: result.error || 'Échec de la création du paiement',
        canRetry: true,
        retryCount,
      };
    }

    // Store new payment record
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        reservation_id: reservationId,
        payment_intent_id: result.paymentIntent!.id,
        amount: reservation.total_amount,
        currency: 'EUR',
        status: 'pending',
        payment_method: 'card',
      });

    if (paymentError) {
      console.error('Error storing retry payment record:', paymentError);
      // Don't fail the request, as the payment intent was created successfully
    }

    return {
      success: true,
      clientSecret: result.clientSecret,
      paymentIntentId: result.paymentIntent!.id,
      retryCount: retryCount + 1,
      canRetry: retryCount + 1 < maxRetries,
    };

  } catch (error) {
    console.error('Error retrying payment:', error);
    return {
      success: false,
      error: 'Erreur interne lors de la nouvelle tentative',
      canRetry: true,
    };
  }
};

// Mark a payment as abandoned and update reservation status
export const abandonPayment = async (reservationId: string, reason?: string): Promise<boolean> => {
  try {
    // Update reservation status to indicate payment failure
    const { error: reservationError } = await supabase
      .from('reservations')
      .update({
        status: 'payment_failed',
        admin_notes: reason ? `Payment abandoned: ${reason}` : 'Payment abandoned by user',
        updated_at: new Date().toISOString(),
      })
      .eq('id', reservationId);

    if (reservationError) {
      console.error('Error updating reservation status:', reservationError);
      return false;
    }

    // Mark all pending payments as failed
    const { error: paymentsError } = await supabase
      .from('payments')
      .update({
        status: 'abandoned',
        failure_reason: reason || 'Payment abandoned by user',
        updated_at: new Date().toISOString(),
      })
      .eq('reservation_id', reservationId)
      .in('status', ['pending', 'processing', 'requires_action']);

    if (paymentsError) {
      console.error('Error updating payment status:', paymentsError);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error abandoning payment:', error);
    return false;
  }
};
