import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// PUT /api/admin/employees/[id]/availability/[availabilityId] - Update employee availability
export const PUT = withAdminAuth(
	async (request: NextRequest, user, { params }: { params: { id: string; availabilityId: string } }) => {
		try {
			const employeeId = params.id;
			const availabilityId = params.availabilityId;
			const availabilityData = await request.json();

			// Validate required fields
			if (
				availabilityData.day_of_week === undefined ||
				!availabilityData.start_time ||
				!availabilityData.end_time
			) {
				return NextResponse.json(
					{ error: "Missing required fields: day_of_week, start_time, end_time" },
					{ status: 400 }
				);
			}

			// Validate day_of_week is between 0-6
			if (availabilityData.day_of_week < 0 || availabilityData.day_of_week > 6) {
				return NextResponse.json({ error: "day_of_week must be between 0 and 6" }, { status: 400 });
			}

			// Validate time format (HH:MM)
			const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
			if (!timeRegex.test(availabilityData.start_time) || !timeRegex.test(availabilityData.end_time)) {
				return NextResponse.json({ error: "Invalid time format. Use HH:MM" }, { status: 400 });
			}

			// Check if the record exists and belongs to the employee
			const { data: existing } = await supabaseAdmin
				.from("employee_availability")
				.select("id")
				.eq("id", availabilityId)
				.eq("employee_id", employeeId)
				.single();

			if (!existing) {
				return NextResponse.json({ error: "Availability record not found" }, { status: 404 });
			}

			// Check if another availability exists for this employee and day (excluding current record)
			const { data: duplicate } = await supabaseAdmin
				.from("employee_availability")
				.select("id")
				.eq("employee_id", employeeId)
				.eq("day_of_week", availabilityData.day_of_week)
				.neq("id", availabilityId)
				.single();

			if (duplicate) {
				return NextResponse.json(
					{ error: "Another availability already exists for this day" },
					{ status: 400 }
				);
			}

			const { data: availability, error } = await supabaseAdmin
				.from("employee_availability")
				.update({
					day_of_week: availabilityData.day_of_week,
					start_time: availabilityData.start_time,
					end_time: availabilityData.end_time,
					is_available: availabilityData.is_available ?? true,
					effective_from: availabilityData.effective_from || null,
					effective_until: availabilityData.effective_until || null,
					updated_at: new Date().toISOString(),
				})
				.eq("id", availabilityId)
				.eq("employee_id", employeeId)
				.select()
				.single();

			if (error) {
				console.error("Error updating employee availability:", error);
				return NextResponse.json({ error: "Failed to update availability" }, { status: 500 });
			}

			return NextResponse.json({
				success: true,
				data: availability,
			});
		} catch (error) {
			console.error("Error in PUT /api/admin/employees/[id]/availability/[availabilityId]:", error);
			return NextResponse.json({ error: "Internal server error" }, { status: 500 });
		}
	}
);

// DELETE /api/admin/employees/[id]/availability/[availabilityId] - Delete employee availability
export const DELETE = withAdminAuth(
	async (request: NextRequest, user, { params }: { params: { id: string; availabilityId: string } }) => {
		try {
			const employeeId = params.id;
			const availabilityId = params.availabilityId;

			// Check if the record exists and belongs to the employee
			const { data: existing } = await supabaseAdmin
				.from("employee_availability")
				.select("id")
				.eq("id", availabilityId)
				.eq("employee_id", employeeId)
				.single();

			if (!existing) {
				return NextResponse.json({ error: "Availability record not found" }, { status: 404 });
			}

			const { error } = await supabaseAdmin
				.from("employee_availability")
				.delete()
				.eq("id", availabilityId)
				.eq("employee_id", employeeId);

			if (error) {
				console.error("Error deleting employee availability:", error);
				return NextResponse.json({ error: "Failed to delete availability" }, { status: 500 });
			}

			return NextResponse.json({
				success: true,
				message: "Availability deleted successfully",
			});
		} catch (error) {
			console.error("Error in DELETE /api/admin/employees/[id]/availability/[availabilityId]:", error);
			return NextResponse.json({ error: "Internal server error" }, { status: 500 });
		}
	}
);
