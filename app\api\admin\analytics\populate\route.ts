import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// POST /api/admin/analytics/populate - Populate historical analytics data
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const {
			startDate,
			endDate,
			force = false,
		}: {
			startDate?: string;
			endDate?: string;
			force?: boolean;
		} = await request.json();

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Default to last 90 days if no dates provided
		const end = endDate ? new Date(endDate) : new Date();
		const start = startDate ? new Date(startDate) : new Date(end.getTime() - 90 * 24 * 60 * 60 * 1000);

		console.log(`Populating analytics data from ${start.toISOString()} to ${end.toISOString()}`);

		// 1. Populate customer analytics for all customers
		const { data: customers } = await supabaseAdmin.from("customers").select("id");

		let customerAnalyticsUpdated = 0;
		if (customers) {
			for (const customer of customers) {
				try {
					await supabaseAdmin.rpc("update_customer_analytics", {
						customer_uuid: customer.id,
					});
					customerAnalyticsUpdated++;
				} catch (error) {
					console.error(`Failed to update analytics for customer ${customer.id}:`, error);
				}
			}
		}

		// 2. Populate daily business metrics
		const dailyMetricsCreated = [];
		const currentDate = new Date(start);

		while (currentDate <= end) {
			const dateStr = currentDate.toISOString().split("T")[0];

			// Check if data already exists
			const { data: existingMetric } = await supabaseAdmin
				.from("daily_business_metrics")
				.select("id")
				.eq("metric_date", dateStr)
				.single();

			if (!existingMetric || force) {
				// Get reservations for this date
				const { data: dayReservations } = await supabaseAdmin
					.from("reservations")
					.select(
						`
            id,
            status,
            total_amount,
            participant_count,
            customer_id,
            created_at
          `
					)
					.gte("created_at", `${dateStr}T00:00:00Z`)
					.lt("created_at", `${dateStr}T23:59:59Z`);

				// Get new customers for this date
				const { data: newCustomers } = await supabaseAdmin
					.from("customers")
					.select("id")
					.gte("created_at", `${dateStr}T00:00:00Z`)
					.lt("created_at", `${dateStr}T23:59:59Z`);

				// Calculate metrics
				const totalReservations = dayReservations?.length || 0;
				const confirmedReservations =
					dayReservations?.filter((r) => r.status === "confirmed" || r.status === "completed") || [];
				const cancelledReservations = dayReservations?.filter((r) => r.status === "cancelled") || [];

				const totalRevenue = confirmedReservations.reduce((sum, r) => sum + (r.total_amount || 0), 0);
				const totalParticipants = confirmedReservations.reduce((sum, r) => sum + (r.participant_count || 0), 0);
				const averageOrderValue =
					confirmedReservations.length > 0 ? totalRevenue / confirmedReservations.length : 0;

				// Count repeat customers (customers who had previous reservations)
				const uniqueCustomers = [...new Set(dayReservations?.map((r) => r.customer_id) || [])];
				let repeatCustomers = 0;

				for (const customerId of uniqueCustomers) {
					const { data: previousReservations } = await supabaseAdmin
						.from("reservations")
						.select("id")
						.eq("customer_id", customerId)
						.lt("created_at", `${dateStr}T00:00:00Z`)
						.limit(1);

					if (previousReservations && previousReservations.length > 0) {
						repeatCustomers++;
					}
				}

				const dailyMetric = {
					metric_date: dateStr,
					total_reservations: totalReservations,
					confirmed_reservations: confirmedReservations.length,
					cancelled_reservations: cancelledReservations.length,
					total_revenue: totalRevenue,
					total_participants: totalParticipants,
					new_customers: newCustomers?.length || 0,
					repeat_customers: repeatCustomers,
					average_order_value: averageOrderValue,
					occupancy_rate: 0, // Would need service capacity data to calculate
					customer_satisfaction: 0, // Would need feedback data
				};

				if (existingMetric && force) {
					await supabaseAdmin.from("daily_business_metrics").update(dailyMetric).eq("id", existingMetric.id);
				} else {
					await supabaseAdmin.from("daily_business_metrics").insert(dailyMetric);
				}

				dailyMetricsCreated.push(dateStr);
			}

			currentDate.setDate(currentDate.getDate() + 1);
		}

		// 3. Populate service analytics (monthly aggregates)
		const { data: services } = await supabaseAdmin.from("services").select("id");

		let serviceAnalyticsCreated = 0;
		if (services) {
			for (const service of services) {
				// Create monthly aggregates
				const monthStart = new Date(start.getFullYear(), start.getMonth(), 1);
				const monthEnd = new Date(end.getFullYear(), end.getMonth() + 1, 0);

				const currentMonth = new Date(monthStart);
				while (currentMonth <= monthEnd) {
					const periodStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
					const periodEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

					const { data: existingAnalytics } = await supabaseAdmin
						.from("service_analytics")
						.select("id")
						.eq("service_id", service.id)
						.eq("period_start", periodStart.toISOString().split("T")[0])
						.eq("period_end", periodEnd.toISOString().split("T")[0])
						.single();

					if (!existingAnalytics || force) {
						// Get service reservations for the month
						const { data: serviceReservations } = await supabaseAdmin
							.from("reservations")
							.select(
								`
                id,
                status,
                total_amount,
                participant_count,
                customer_feedback (rating)
              `
							)
							.eq("service_id", service.id)
							.gte("created_at", periodStart.toISOString())
							.lte("created_at", periodEnd.toISOString());

						const totalBookings = serviceReservations?.length || 0;
						const confirmedBookings =
							serviceReservations?.filter((r) => r.status === "confirmed" || r.status === "completed") ||
							[];
						const cancelledBookings = serviceReservations?.filter((r) => r.status === "cancelled") || [];

						const totalRevenue = confirmedBookings.reduce((sum, r) => sum + (r.total_amount || 0), 0);
						const totalParticipants = confirmedBookings.reduce(
							(sum, r) => sum + (r.participant_count || 0),
							0
						);
						const avgGroupSize =
							confirmedBookings.length > 0 ? totalParticipants / confirmedBookings.length : 0;

						const ratings =
							serviceReservations?.flatMap((r) => r.customer_feedback || []).map((f) => f.rating) || [];
						const averageRating =
							ratings.length > 0 ? ratings.reduce((sum, r) => sum + r, 0) / ratings.length : 0;

						const cancellationRate =
							totalBookings > 0 ? (cancelledBookings.length / totalBookings) * 100 : 0;

						const serviceAnalytic = {
							service_id: service.id,
							period_start: periodStart.toISOString().split("T")[0],
							period_end: periodEnd.toISOString().split("T")[0],
							total_bookings: totalBookings,
							confirmed_bookings: confirmedBookings.length,
							cancelled_bookings: cancelledBookings.length,
							total_revenue: totalRevenue,
							total_participants: totalParticipants,
							average_group_size: avgGroupSize,
							average_rating: averageRating,
							total_reviews: ratings.length,
							cancellation_rate: cancellationRate,
						};

						if (existingAnalytics && force) {
							await supabaseAdmin
								.from("service_analytics")
								.update(serviceAnalytic)
								.eq("id", existingAnalytics.id);
						} else {
							await supabaseAdmin.from("service_analytics").insert(serviceAnalytic);
						}

						serviceAnalyticsCreated++;
					}

					currentMonth.setMonth(currentMonth.getMonth() + 1);
				}
			}
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"POPULATE_ANALYTICS",
			"analytics",
			null,
			null,
			{
				startDate: start.toISOString(),
				endDate: end.toISOString(),
				customerAnalyticsUpdated,
				dailyMetricsCreated: dailyMetricsCreated.length,
				serviceAnalyticsCreated,
				force,
			},
			request
		);

		return NextResponse.json({
			success: true,
			message: "Analytics data populated successfully",
			results: {
				customerAnalyticsUpdated,
				dailyMetricsCreated: dailyMetricsCreated.length,
				serviceAnalyticsCreated,
				dateRange: {
					start: start.toISOString(),
					end: end.toISOString(),
				},
			},
		});
	} catch (error) {
		console.error("Analytics populate error:", error);
		return NextResponse.json({ error: "Failed to populate analytics data" }, { status: 500 });
	}
}, "analytics:read");
