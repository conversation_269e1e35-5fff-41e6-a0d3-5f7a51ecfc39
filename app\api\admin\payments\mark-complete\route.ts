import { withAdminAuth } from "@/lib/admin-auth";
import { createNotification, createPaymentReceivedNotification } from "@/lib/notifications";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// POST /api/admin/payments/mark-complete - Mark remaining payment as complete
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { reservationId, amount, paymentMethod = "cash", notes } = await request.json();

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		if (!amount || amount <= 0) {
			return NextResponse.json({ error: "Valid payment amount is required" }, { status: 400 });
		}

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Get reservation details
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        *,
        customer:customers(first_name, last_name, email),
        service:services(name)
      `
			)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		// Check if there's already a deposit payment
		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		const { data: existingPayments } = await supabaseAdmin
			.from("payments")
			.select("*")
			.eq("reservation_id", reservationId)
			.eq("status", "succeeded");

		const hasDepositPayment = existingPayments?.some((p: any) => p.is_deposit);
		const totalPaid = existingPayments?.reduce((sum: number, p: any) => sum + p.amount, 0) || 0;
		const remainingAmount = reservation.total_amount - totalPaid;

		if (amount > remainingAmount) {
			return NextResponse.json(
				{ error: `Payment amount (${amount}€) exceeds remaining balance (${remainingAmount}€)` },
				{ status: 400 }
			);
		}

		// Create manual payment record
		const { data: payment, error: paymentError } = await supabaseAdmin
			.from("payments")
			.insert({
				reservation_id: reservationId,
				payment_intent_id: null, // No Stripe payment intent for manual payments
				amount: amount,
				currency: "EUR",
				status: "succeeded",
				payment_method: paymentMethod,
				payment_type: hasDepositPayment ? "remaining" : "full",
				is_deposit: false,
				completed_manually: true,
				completed_by: user.id,
				completion_notes: notes,
				manual_completion_date: new Date().toISOString(),
				payment_date: new Date().toISOString(),
			})
			.select()
			.single();

		if (paymentError) {
			console.error("Error creating manual payment record:", paymentError);
			return NextResponse.json({ error: "Failed to create payment record" }, { status: 500 });
		}

		// Update reservation status and remaining amount
		const newRemainingAmount = remainingAmount - amount;
		const isFullyPaid = newRemainingAmount <= 0;

		const reservationUpdate: any = {
			updated_at: new Date().toISOString(),
		};

		if (isFullyPaid) {
			reservationUpdate.status = "confirmed";
			reservationUpdate.remaining_amount = 0;
			if (!reservation.confirmed_at) {
				reservationUpdate.confirmed_at = new Date().toISOString();
				reservationUpdate.confirmed_by = user.id;
			}
		} else {
			reservationUpdate.remaining_amount = newRemainingAmount;
		}

		const { error: reservationUpdateError } = await supabaseAdmin
			.from("reservations")
			.update(reservationUpdate)
			.eq("id", reservationId);

		if (reservationUpdateError) {
			console.error("Error updating reservation:", reservationUpdateError);
			// Don't fail the request as payment was recorded successfully
		}

		// Add reservation status history
		try {
			await supabaseAdmin.from("reservation_status_history").insert({
				reservation_id: reservationId,
				old_status: reservation.status,
				new_status: isFullyPaid ? "confirmed" : reservation.status,
				changed_by: user.id,
				change_reason: `Manual payment completion: ${amount}€ (${paymentMethod})`,
			});
		} catch (historyError) {
			console.error("Error adding status history:", historyError);
			// Don't fail the request
		}

		// Create payment received notification for other admins
		try {
			const { data: adminProfiles } = await supabaseAdmin
				.from("profiles")
				.select("id")
				.eq("role", "admin")
				.neq("id", user.id); // Exclude the current user

			if (adminProfiles) {
				const notificationTemplate = createPaymentReceivedNotification(amount, "EUR", reservationId);

				for (const admin of adminProfiles) {
					await createNotification(admin.id, notificationTemplate, reservationId);
				}
			}
		} catch (notificationError) {
			console.error("Error creating payment notification:", notificationError);
			// Don't fail the request
		}

		return NextResponse.json({
			success: true,
			payment: {
				id: payment.id,
				amount: payment.amount,
				currency: payment.currency,
				paymentMethod: payment.payment_method,
				paymentType: payment.payment_type,
				completedManually: true,
				completedBy: user.id,
				completionDate: payment.manual_completion_date,
			},
			reservation: {
				id: reservationId,
				totalAmount: reservation.total_amount,
				totalPaid: totalPaid + amount,
				remainingAmount: newRemainingAmount,
				isFullyPaid,
				status: isFullyPaid ? "confirmed" : reservation.status,
			},
			message: isFullyPaid
				? "Payment completed successfully. Reservation is now fully paid."
				: `Payment of ${amount}€ recorded. Remaining balance: ${newRemainingAmount}€`,
		});
	} catch (error) {
		console.error("Error marking payment as complete:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
});
