import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type DiscountCoupon = Database["public"]["Tables"]["discount_coupons"]["Row"];
type DiscountCouponInsert = Database["public"]["Tables"]["discount_coupons"]["Insert"];
type DiscountCouponUpdate = Database["public"]["Tables"]["discount_coupons"]["Update"];

// GET /api/admin/discount-coupons - List all discount coupons
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const search = searchParams.get("search");
		const isActive = searchParams.get("active");
		const discountType = searchParams.get("discount_type");
		const offset = (page - 1) * limit;

		// Build query
		let query = supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (search) {
			query = query.or(`code.ilike.%${search}%,description.ilike.%${search}%`);
		}
		if (isActive !== null) {
			query = query.eq("is_active", isActive === "true");
		}
		if (discountType) {
			query = query.eq("discount_type", discountType);
		}

		const { data: discountCoupons, error } = await query;

		if (error) {
			console.error("Error fetching discount coupons:", error);
			return NextResponse.json({ error: "Failed to fetch discount coupons" }, { status: 500 });
		}

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin
			.from("discount_coupons")
			.select("*", { count: "exact", head: true });

		return NextResponse.json({
			discountCoupons: discountCoupons || [],
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Discount coupons GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// POST /api/admin/discount-coupons - Create new discount coupon
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const couponData: DiscountCouponInsert = await request.json();

		// Validate required fields
		if (!couponData.code || !couponData.discount_type || !couponData.discount_value) {
			return NextResponse.json(
				{ error: "Missing required fields: code, discount_type, discount_value" },
				{ status: 400 }
			);
		}

		// Validate discount type and value
		if (couponData.discount_type === "percentage" && couponData.discount_value > 100) {
			return NextResponse.json({ error: "Percentage discount cannot exceed 100%" }, { status: 400 });
		}

		if (couponData.discount_value <= 0) {
			return NextResponse.json({ error: "Discount value must be greater than 0" }, { status: 400 });
		}

		// Check if coupon code already exists
		const { data: existingCoupon } = await supabaseAdmin
			.from("discount_coupons")
			.select("id")
			.eq("code", couponData.code)
			.single();

		if (existingCoupon) {
			return NextResponse.json({ error: "Coupon code already exists" }, { status: 400 });
		}

		// Set created_by to current user
		couponData.created_by = user.id;

		// Create discount coupon
		const { data: discountCoupon, error } = await supabaseAdmin
			.from("discount_coupons")
			.insert(couponData)
			.select()
			.single();

		if (error) {
			console.error("Error creating discount coupon:", error);
			return NextResponse.json({ error: "Failed to create discount coupon" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "create", "discount_coupons", discountCoupon.id, null, discountCoupon);

		return NextResponse.json({ discountCoupon });
	} catch (error) {
		console.error("Discount coupon POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// PUT /api/admin/discount-coupons - Bulk update discount coupons
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { couponIds, updates }: { couponIds: string[]; updates: DiscountCouponUpdate } = await request.json();

		if (!couponIds || couponIds.length === 0) {
			return NextResponse.json({ error: "No coupon IDs provided" }, { status: 400 });
		}

		// Get current coupons for audit log
		const { data: currentCoupons } = await supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.in("id", couponIds);

		// Update discount coupons
		const { data: updatedCoupons, error } = await supabaseAdmin
			.from("discount_coupons")
			.update(updates)
			.in("id", couponIds)
			.select();

		if (error) {
			console.error("Error updating discount coupons:", error);
			return NextResponse.json({ error: "Failed to update discount coupons" }, { status: 500 });
		}

		// Log admin actions
		for (const currentCoupon of currentCoupons || []) {
			const updatedCoupon = updatedCoupons?.find((c) => c.id === currentCoupon.id);
			if (updatedCoupon) {
				await logAdminAction(user.id, "update", "discount_coupons", currentCoupon.id, currentCoupon, updatedCoupon);
			}
		}

		return NextResponse.json({
			message: `Updated ${updatedCoupons?.length || 0} discount coupons`,
			discountCoupons: updatedCoupons,
		});
	} catch (error) {
		console.error("Discount coupons PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/discount-coupons - Deactivate discount coupons
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { couponIds }: { couponIds: string[] } = await request.json();

		if (!couponIds || couponIds.length === 0) {
			return NextResponse.json({ error: "No coupon IDs provided" }, { status: 400 });
		}

		// Get coupons for audit log before deactivation
		const { data: couponsToDeactivate } = await supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.in("id", couponIds);

		// Soft delete by setting is_active to false
		const { error } = await supabaseAdmin
			.from("discount_coupons")
			.update({ is_active: false })
			.in("id", couponIds);

		if (error) {
			console.error("Error deactivating discount coupons:", error);
			return NextResponse.json({ error: "Failed to deactivate discount coupons" }, { status: 500 });
		}

		// Log admin actions
		for (const coupon of couponsToDeactivate || []) {
			await logAdminAction(
				user.id,
				"delete",
				"discount_coupons",
				coupon.id,
				coupon,
				{ ...coupon, is_active: false }
			);
		}

		return NextResponse.json({
			message: `Deactivated ${couponIds.length} discount coupons`,
		});
	} catch (error) {
		console.error("Discount coupons DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");
