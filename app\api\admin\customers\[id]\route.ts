import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Customer = Database["public"]["Tables"]["customers"]["Row"];
type CustomerUpdate = Database["public"]["Tables"]["customers"]["Update"];

// GET /api/admin/customers/[id] - Get single customer with full details
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const customerId = params.id;

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		const { data: customer, error } = await supabaseAdmin
			.from("customers")
			.select(
				`
        *,
        profile:profiles!customers_id_fkey (
          id,
          email,
          first_name,
          last_name,
          phone,
          role,
          created_at
        ),
        reservations (
          id,
          reservation_number,
          start_time,
          end_time,
          status,
          total_amount,
          participant_count,
          special_requests,
          admin_notes,
          service:services (
            id,
            name,
            category,
            image_url
          ),
          assigned_employee:employees (
            id,
            first_name,
            last_name
          ),
          payments (
            id,
            amount,
            currency,
            status,
            payment_method,
            payment_date
          )
        ),
        customer_feedback (
          id,
          rating,
          review_text,
          service_quality_rating,
          staff_rating,
          equipment_rating,
          would_recommend,
          is_public,
          created_at,
          reservation:reservations (
            id,
            service:services (
              name
            )
          )
        ),
        customer_analytics (
          total_reservations,
          completed_reservations,
          cancelled_reservations,
          total_spent,
          total_participants,
          average_rating,
          total_reviews,
          first_reservation_date,
          last_reservation_date,
          customer_lifetime_value,
          loyalty_tier
        ),
        customer_journey_events (
          id,
          event_type,
          event_data,
          created_at
        )
      `
			)
			.eq("id", customerId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Customer not found" }, { status: 404 });
			}
			console.error("Error fetching customer:", error);
			return NextResponse.json({ error: "Failed to fetch customer" }, { status: 500 });
		}

		// Calculate additional statistics
		const reservations = customer.reservations || [];
		const feedback = customer.customer_feedback || [];

		const upcomingReservations = reservations.filter(
			(r) => new Date(r.start_time) > new Date() && r.status !== "cancelled"
		);

		const recentActivity =
			customer.customer_journey_events
				?.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
				.slice(0, 10) || [];

		const preferredServices = reservations
			.filter((r) => r.status === "completed")
			.reduce((acc, r) => {
				const serviceName = r.service?.name || "Unknown";
				acc[serviceName] = (acc[serviceName] || 0) + 1;
				return acc;
			}, {} as Record<string, number>);

		const topPreferredServices = Object.entries(preferredServices)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 3)
			.map(([name, count]) => ({ name, count }));

		return NextResponse.json({
			customer: {
				...customer,
				stats: {
					upcomingReservations: upcomingReservations.length,
					totalSpent: customer.customer_analytics?.[0]?.total_spent || 0,
					averageRating: customer.customer_analytics?.[0]?.average_rating || 0,
					loyaltyTier: customer.customer_analytics?.[0]?.loyalty_tier || "bronze",
					lifetimeValue: customer.customer_analytics?.[0]?.customer_lifetime_value || 0,
				},
				insights: {
					preferredServices: topPreferredServices,
					recentActivity,
					bookingPatterns: {
						averageGroupSize:
							reservations.length > 0
								? reservations.reduce((sum, r) => sum + (r.participant_count || 0), 0) /
								  reservations.length
								: 0,
						mostActiveMonth: null, // Could be calculated from reservation dates
						preferredTimeSlots: null, // Could be calculated from reservation times
					},
				},
			},
		});
	} catch (error) {
		console.error("Customer GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:read");

// PUT /api/admin/customers/[id] - Update single customer
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const customerId = params.id;
		const updates: CustomerUpdate = await request.json();

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Get current customer for audit log
		const { data: currentCustomer } = await supabaseAdmin
			.from("customers")
			.select("*")
			.eq("id", customerId)
			.single();

		if (!currentCustomer) {
			return NextResponse.json({ error: "Customer not found" }, { status: 404 });
		}

		// Update customer
		const { data: updatedCustomer, error } = await supabaseAdmin
			.from("customers")
			.update(updates)
			.eq("id", customerId)
			.select()
			.single();

		if (error) {
			console.error("Error updating customer:", error);
			return NextResponse.json({ error: "Failed to update customer" }, { status: 500 });
		}

		// Update customer analytics
		await supabaseAdmin.rpc("update_customer_analytics", {
			customer_uuid: customerId,
		});

		return NextResponse.json({ customer: updatedCustomer });
	} catch (error) {
		console.error("Customer PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:write");

// DELETE /api/admin/customers/[id] - Delete customer (with data protection checks)
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const customerId = params.id;

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Get customer for audit log
		const { data: customerToDelete } = await supabaseAdmin
			.from("customers")
			.select("*")
			.eq("id", customerId)
			.single();

		if (!customerToDelete) {
			return NextResponse.json({ error: "Customer not found" }, { status: 404 });
		}

		// Check for existing reservations
		const { data: existingReservations } = await supabaseAdmin
			.from("reservations")
			.select("id, status")
			.eq("customer_id", customerId);

		const activeReservations =
			existingReservations?.filter((r) => r.status === "confirmed" || r.status === "pending") || [];

		if (activeReservations.length > 0) {
			return NextResponse.json(
				{
					error: "Cannot delete customer with active reservations",
					activeReservations: activeReservations.length,
				},
				{ status: 400 }
			);
		}

		// For GDPR compliance, we should anonymize rather than delete
		// This preserves business analytics while removing personal data
		const anonymizedData = {
			date_of_birth: null,
			nationality: null,
			emergency_contact_name: null,
			emergency_contact_phone: null,
			dietary_restrictions: null,
			medical_conditions: null,
			marketing_consent: false,
		};

		// Anonymize customer data
		const { error: customerError } = await supabaseAdmin
			.from("customers")
			.update(anonymizedData)
			.eq("id", customerId);

		if (customerError) {
			console.error("Error anonymizing customer:", customerError);
			return NextResponse.json({ error: "Failed to delete customer" }, { status: 500 });
		}

		// Anonymize profile data
		const { error: profileError } = await supabaseAdmin
			.from("profiles")
			.update({
				email: `deleted-${customerId}@anonymized.local`,
				first_name: "Deleted",
				last_name: "User",
				phone: null,
			})
			.eq("id", customerId);

		if (profileError) {
			console.error("Error anonymizing profile:", profileError);
		}

		// Log admin action
		await logAdminAction(user.id, "ANONYMIZE", "customers", customerId, customerToDelete, anonymizedData, request);

		return NextResponse.json({
			message: "Customer data anonymized successfully (GDPR compliant)",
			customerId,
		});
	} catch (error) {
		console.error("Customer DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:delete");
