import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// POST /api/admin/service-equipment-requirements - Create new service equipment requirement
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const requirementData = await request.json();

		// Validate required fields
		if (!requirementData.service_id || !requirementData.equipment_id) {
			return NextResponse.json({ error: "Service ID and Equipment ID are required" }, { status: 400 });
		}

		// Check if requirement already exists
		const { data: existing } = await supabaseAdmin
			.from("service_equipment_requirements")
			.select("id")
			.eq("service_id", requirementData.service_id)
			.eq("equipment_id", requirementData.equipment_id)
			.single();

		if (existing) {
			return NextResponse.json(
				{ error: "Equipment requirement already exists for this service" },
				{ status: 409 }
			);
		}

		// Create the requirement
		const { data: requirement, error } = await supabaseAdmin
			.from("service_equipment_requirements")
			.insert(requirementData)
			.select(
				`
				*,
				service:services(id, name),
				equipment:equipment(id, name, total_capacity)
			`
			)
			.single();

		if (error) {
			console.error("Error creating service equipment requirement:", error);
			return NextResponse.json({ error: "Failed to create requirement" }, { status: 500 });
		}

		return NextResponse.json({
			message: "Service equipment requirement created successfully",
			requirement,
		});
	} catch (error) {
		console.error("Service equipment requirement POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");
