import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type CustomerFeedback = Database["public"]["Tables"]["customer_feedback"]["Row"];
type CustomerFeedbackUpdate = Database["public"]["Tables"]["customer_feedback"]["Update"];

// GET /api/admin/customer-feedback - List all customer feedback
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const search = searchParams.get("search");
		const rating = searchParams.get("rating");
		const isPublic = searchParams.get("is_public");
		const hasResponse = searchParams.get("has_response");
		const serviceId = searchParams.get("service_id");
		const offset = (page - 1) * limit;

		// Build query
		let query = supabaseAdmin
			.from("customer_feedback")
			.select(
				`
        *,
        customer:customers (
          id,
          first_name,
          last_name,
          email
        ),
        reservation:reservations (
          id,
          reservation_number,
          start_time,
          service:services (
            id,
            name,
            category
          )
        ),
        responder:profiles!customer_feedback_responded_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `
			)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (search) {
			query = query.or(`review_text.ilike.%${search}%,response_text.ilike.%${search}%`);
		}
		if (rating) {
			query = query.eq("rating", parseInt(rating));
		}
		if (isPublic !== null) {
			query = query.eq("is_public", isPublic === "true");
		}
		if (hasResponse !== null) {
			if (hasResponse === "true") {
				query = query.not("response_text", "is", null);
			} else {
				query = query.is("response_text", null);
			}
		}
		if (serviceId) {
			query = query.eq("reservation.service_id", serviceId);
		}

		const { data: feedback, error } = await query;

		if (error) {
			console.error("Error fetching customer feedback:", error);
			return NextResponse.json({ error: "Failed to fetch customer feedback" }, { status: 500 });
		}

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin
			.from("customer_feedback")
			.select("*", { count: "exact", head: true });

		return NextResponse.json({
			feedback: feedback || [],
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Customer feedback GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:read");

// PUT /api/admin/customer-feedback - Bulk update customer feedback
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { feedbackIds, updates }: { feedbackIds: string[]; updates: CustomerFeedbackUpdate } =
			await request.json();

		if (!feedbackIds || feedbackIds.length === 0) {
			return NextResponse.json({ error: "No feedback IDs provided" }, { status: 400 });
		}

		// Get current feedback for audit log
		const { data: currentFeedback } = await supabaseAdmin
			.from("customer_feedback")
			.select("*")
			.in("id", feedbackIds);

		// Update customer feedback
		const { data: updatedFeedback, error } = await supabaseAdmin
			.from("customer_feedback")
			.update(updates)
			.in("id", feedbackIds)
			.select();

		if (error) {
			console.error("Error updating customer feedback:", error);
			return NextResponse.json({ error: "Failed to update customer feedback" }, { status: 500 });
		}

		// Log admin actions
		for (const currentItem of currentFeedback || []) {
			const updatedItem = updatedFeedback?.find((f) => f.id === currentItem.id);
			if (updatedItem) {
				await logAdminAction(user.id, "update", "customer_feedback", currentItem.id, currentItem, updatedItem);
			}
		}

		return NextResponse.json({
			message: `Updated ${updatedFeedback?.length || 0} feedback items`,
			feedback: updatedFeedback,
		});
	} catch (error) {
		console.error("Customer feedback PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:write");
