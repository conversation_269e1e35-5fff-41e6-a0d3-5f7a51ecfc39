import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type CustomerFeedback = Database["public"]["Tables"]["customer_feedback"]["Row"];
type CustomerFeedbackUpdate = Database["public"]["Tables"]["customer_feedback"]["Update"];

// GET /api/admin/customer-feedback/[id] - Get single customer feedback
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const feedbackId = params.id;

		const { data: feedback, error } = await supabaseAdmin
			.from("customer_feedback")
			.select(
				`
        *,
        customer:customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        reservation:reservations (
          id,
          reservation_number,
          start_time,
          end_time,
          participant_count,
          total_amount,
          service:services (
            id,
            name,
            category,
            image_url
          ),
          assigned_employee:employees (
            id,
            first_name,
            last_name
          )
        ),
        responder:profiles!customer_feedback_responded_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `
			)
			.eq("id", feedbackId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Customer feedback not found" }, { status: 404 });
			}
			console.error("Error fetching customer feedback:", error);
			return NextResponse.json({ error: "Failed to fetch customer feedback" }, { status: 500 });
		}

		return NextResponse.json({ feedback });
	} catch (error) {
		console.error("Customer feedback GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:read");

// PUT /api/admin/customer-feedback/[id] - Update single customer feedback
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const feedbackId = params.id;
		const updates: CustomerFeedbackUpdate = await request.json();

		// Get current feedback for audit log
		const { data: currentFeedback } = await supabaseAdmin
			.from("customer_feedback")
			.select("*")
			.eq("id", feedbackId)
			.single();

		if (!currentFeedback) {
			return NextResponse.json({ error: "Customer feedback not found" }, { status: 404 });
		}

		// If adding a response, set responded_by and responded_at
		if (updates.response_text && !currentFeedback.response_text) {
			updates.responded_by = user.id;
			updates.responded_at = new Date().toISOString();
		}

		// Update customer feedback
		const { data: updatedFeedback, error } = await supabaseAdmin
			.from("customer_feedback")
			.update(updates)
			.eq("id", feedbackId)
			.select()
			.single();

		if (error) {
			console.error("Error updating customer feedback:", error);
			return NextResponse.json({ error: "Failed to update customer feedback" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "update", "customer_feedback", feedbackId, currentFeedback, updatedFeedback);

		return NextResponse.json({ feedback: updatedFeedback });
	} catch (error) {
		console.error("Customer feedback PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:write");

// POST /api/admin/customer-feedback/[id]/respond - Add response to customer feedback
export const POST = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const feedbackId = params.id;
		const { response_text, is_public }: { response_text: string; is_public?: boolean } = await request.json();

		if (!response_text || response_text.trim() === "") {
			return NextResponse.json({ error: "Response text is required" }, { status: 400 });
		}

		// Get current feedback for audit log
		const { data: currentFeedback } = await supabaseAdmin
			.from("customer_feedback")
			.select("*")
			.eq("id", feedbackId)
			.single();

		if (!currentFeedback) {
			return NextResponse.json({ error: "Customer feedback not found" }, { status: 404 });
		}

		// Update with response
		const updates: CustomerFeedbackUpdate = {
			response_text: response_text.trim(),
			responded_by: user.id,
			responded_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
		};

		if (is_public !== undefined) {
			updates.is_public = is_public;
		}

		const { data: updatedFeedback, error } = await supabaseAdmin
			.from("customer_feedback")
			.update(updates)
			.eq("id", feedbackId)
			.select()
			.single();

		if (error) {
			console.error("Error responding to customer feedback:", error);
			return NextResponse.json({ error: "Failed to respond to customer feedback" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "update", "customer_feedback", feedbackId, currentFeedback, updatedFeedback);

		return NextResponse.json({ 
			message: "Response added successfully",
			feedback: updatedFeedback 
		});
	} catch (error) {
		console.error("Customer feedback respond error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "customers:write");
