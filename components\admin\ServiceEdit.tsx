"use client";

import { adminApi } from "@/lib/api-client";
import { Service, ServiceInsert } from "@/lib/types";
import { ServiceOption, ServiceOptionType } from "@/lib/types/services";
import { AlertCircle, ArrowLeft, Loader2, Plus, Save, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import ImageUpload from "./ImageUpload";
import Button from "./ui/Button";

interface ServiceEditProps {
	serviceId: string;
}

const ServiceEdit = ({ serviceId }: ServiceEditProps) => {
	const router = useRouter();
	const [service, setService] = useState<Service | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [editForm, setEditForm] = useState<Partial<ServiceInsert>>({});
	const [features, setFeatures] = useState<string[]>([]);
	const [editFeatures, setEditFeatures] = useState<string[]>([]);
	const [newFeature, setNewFeature] = useState("");
	const [options, setOptions] = useState<ServiceOption[]>([]);
	const [editOptions, setEditOptions] = useState<ServiceOption[]>([]);

	useEffect(() => {
		fetchService();
		fetchFeatures();
	}, [serviceId]);

	const fetchService = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getService(serviceId);
			if (response?.service) {
				const serviceData = response.service;
				setService(serviceData);

				// Parse options from JSONB
				const serviceOptions = Array.isArray(serviceData.options) ? serviceData.options : [];
				setOptions(serviceOptions);
				setEditOptions([...serviceOptions]);

				setEditForm({
					name: serviceData.name,
					description: serviceData.description || "",
					duration_minutes: serviceData.duration_minutes,
					buffer_time_minutes: serviceData.buffer_time_minutes || 0,
					base_price: serviceData.base_price,
					max_participants: serviceData.max_participants,
					min_age: serviceData.min_age || undefined,
					max_age: serviceData.max_age || undefined,
					is_family_friendly: serviceData.is_family_friendly || false,
					category: serviceData.category || "",
					location: serviceData.location || "",
					image_url: serviceData.image_url || "",
					features: serviceData.features || [],
					is_active: serviceData.is_active,
					requires_employee: serviceData.requires_employee,
					requires_qualification: serviceData.requires_qualification || false,
					auto_assign_employees: serviceData.auto_assign_employees || false,
				});

				// Handle features
				let serviceFeatures: string[] = [];
				if (Array.isArray(serviceData.features)) {
					serviceFeatures = serviceData.features;
				} else if (serviceData.features && typeof serviceData.features === "object") {
					serviceFeatures = Object.values(serviceData.features).filter((f) => typeof f === "string");
				} else if (typeof serviceData.features === "string") {
					try {
						const parsed = JSON.parse(serviceData.features);
						serviceFeatures = Array.isArray(parsed) ? parsed : [];
					} catch {
						serviceFeatures = [serviceData.features];
					}
				}
				setFeatures(serviceFeatures);
				setEditFeatures([...serviceFeatures]);
			}
		} catch (err) {
			console.error("Error fetching service:", err);
			setError("Erreur lors du chargement du service");
		} finally {
			setLoading(false);
		}
	};

	const fetchFeatures = async () => {
		// Features are now fetched in fetchService
	};

	const addFeature = () => {
		if (newFeature.trim()) {
			setEditFeatures([...editFeatures, newFeature.trim()]);
			setNewFeature("");
		}
	};

	const removeFeature = (index: number) => {
		setEditFeatures(editFeatures.filter((_, i) => i !== index));
	};

	const updateFeature = (index: number, value: string) => {
		const updated = [...editFeatures];
		updated[index] = value;
		setEditFeatures(updated);
	};

	// Option management functions
	const addOption = () => {
		const newOption: ServiceOption = {
			id: `option_${Date.now()}`,
			name: "",
			description: "",
			price: 0,
			type: "optional",
			required: false,
			quote_based: false,
			per_participant: false,
		};
		setEditOptions([...editOptions, newOption]);
	};

	const removeOption = (index: number) => {
		setEditOptions(editOptions.filter((_, i) => i !== index));
	};

	const updateOption = (index: number, field: keyof ServiceOption, value: any) => {
		const updated = [...editOptions];
		updated[index] = { ...updated[index], [field]: value };
		setEditOptions(updated);
	};

	const handleInputChange = (field: keyof ServiceInsert, value: any) => {
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, [field]: value }));
	};

	const handleSave = async () => {
		if (!editForm.name || !editForm.duration_minutes || !editForm.base_price || !editForm.max_participants) {
			setError("Veuillez remplir tous les champs obligatoires");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			// Filter out empty features
			const cleanFeatures = editFeatures.filter((feature) => feature.trim() !== "");

			// Validate and clean options
			const cleanOptions = editOptions.filter((option) => option.name.trim() !== "");

			// Validate option configurations
			for (const option of cleanOptions) {
				if (!option.name.trim()) {
					setError("Tous les options doivent avoir un nom");
					return;
				}
				if (option.price < 0) {
					setError("Le prix des options ne peut pas être négatif");
					return;
				}
				if (option.type === "required_min" && (!option.min_selections || option.min_selections < 1)) {
					setError("Les options 'minimum requis' doivent avoir un nombre minimum de sélections");
					return;
				}
			}

			await adminApi.updateService(serviceId, {
				...editForm,
				features: cleanFeatures,
				options: cleanOptions,
			});

			setFeatures(cleanFeatures);
			setOptions(cleanOptions);
			// Refresh service data
			await fetchService();
		} catch (err) {
			console.error("Error saving service:", err);
			setError("Erreur lors de la sauvegarde du service");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		router.push("/admin/services");
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	if (!service) {
		return (
			<div className="p-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">Service non trouvé</h1>
					<Button onClick={() => router.push("/admin/services")} icon={ArrowLeft}>
						Retour aux services
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			{/* Header */}
			<div className="flex items-center gap-4 mb-8">
				<Button variant="outline" onClick={handleCancel} icon={ArrowLeft}>
					Retour
				</Button>
				<div>
					<h1 className="text-3xl font-bold text-gray-900">Modifier le service</h1>
					<p className="text-gray-600">Modifiez les informations de base du service</p>
				</div>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Edit Form */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* Basic Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Informations de base</h3>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Nom du service *</label>
							<input
								type="text"
								value={editForm.name || ""}
								onChange={(e) => handleInputChange("name", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Nom du service"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Catégorie</label>
							<input
								type="text"
								value={editForm.category || ""}
								onChange={(e) => handleInputChange("category", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: Plongée, Kayak, Randonnée..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Lieu</label>
							<input
								type="text"
								value={editForm.location || ""}
								onChange={(e) => handleInputChange("location", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Lieu de l'activité"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
							<textarea
								value={editForm.description || ""}
								onChange={(e) => handleInputChange("description", e.target.value)}
								rows={4}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Description détaillée du service"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Image du service</label>
							<ImageUpload
								value={editForm.image_url || ""}
								onChange={(url) => handleInputChange("image_url", url)}
								disabled={saving}
							/>
						</div>
					</div>

					{/* Pricing and Capacity */}
					<div className="space-y-4">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Tarifs et capacité</h3>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Prix de base (€) *</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.base_price || 0}
								onChange={(e) => handleInputChange("base_price", parseFloat(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Durée (minutes) *</label>
							<input
								type="number"
								min="1"
								value={editForm.duration_minutes || 120}
								onChange={(e) => handleInputChange("duration_minutes", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Temps de préparation (minutes)
							</label>
							<input
								type="number"
								min="0"
								value={editForm.buffer_time_minutes || 0}
								onChange={(e) => handleInputChange("buffer_time_minutes", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Participants maximum *
							</label>
							<input
								type="number"
								min="1"
								value={editForm.max_participants || 10}
								onChange={(e) => handleInputChange("max_participants", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Âge minimum</label>
								<input
									type="number"
									min="0"
									max="120"
									value={editForm.min_age || ""}
									onChange={(e) =>
										handleInputChange("min_age", e.target.value ? parseInt(e.target.value) : null)
									}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									placeholder="Aucune limite"
								/>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Âge maximum</label>
								<input
									type="number"
									min="0"
									max="120"
									value={editForm.max_age || ""}
									onChange={(e) =>
										handleInputChange("max_age", e.target.value ? parseInt(e.target.value) : null)
									}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									placeholder="Aucune limite"
								/>
							</div>
						</div>

						{/* Options */}
						<div className="space-y-3">
							<h4 className="text-md font-medium text-gray-900">Options</h4>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.is_active || false}
									onChange={(e) => handleInputChange("is_active", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Service actif</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.requires_employee || false}
									onChange={(e) => handleInputChange("requires_employee", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Nécessite un employé</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.requires_qualification || false}
									onChange={(e) => handleInputChange("requires_qualification", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Nécessite une qualification spécifique</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.auto_assign_employees || false}
									onChange={(e) => handleInputChange("auto_assign_employees", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Attribution automatique des employés</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.is_family_friendly || false}
									onChange={(e) => handleInputChange("is_family_friendly", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Adapté aux familles</span>
							</label>
						</div>
					</div>

					{/* Features Section */}
					<div className="mt-8">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Caractéristiques du service</h3>
						<div className="space-y-3">
							{editFeatures.map((feature, index) => (
								<div key={index} className="flex items-center gap-2">
									<input
										type="text"
										value={feature}
										onChange={(e) => updateFeature(index, e.target.value)}
										placeholder="Caractéristique du service"
										className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
									<button
										type="button"
										onClick={() => removeFeature(index)}
										className="text-red-600 hover:text-red-800 p-2"
									>
										<X className="w-4 h-4" />
									</button>
								</div>
							))}

							{/* Add new feature */}
							<div className="flex items-center gap-2 pt-4 border-t">
								<input
									type="text"
									value={newFeature}
									onChange={(e) => setNewFeature(e.target.value)}
									placeholder="Nouvelle caractéristique"
									className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									onKeyPress={(e) => {
										if (e.key === "Enter") {
											addFeature();
										}
									}}
								/>
								<button
									type="button"
									onClick={addFeature}
									disabled={!newFeature.trim()}
									className="px-3 py-2 text-sm text-emerald-600 border border-emerald-300 rounded-lg hover:bg-emerald-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<Plus className="w-4 h-4" />
								</button>
							</div>
						</div>
						<p className="text-xs text-gray-500 mt-2">
							Ajoutez les caractéristiques et avantages inclus dans ce service
						</p>
					</div>

					{/* Service Options Section */}
					<div className="mt-8">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Options du service</h3>
						<div className="space-y-4">
							{editOptions.map((option, index) => (
								<div key={option.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										{/* Option Name */}
										<div>
											<label className="block text-sm font-medium text-gray-700 mb-1">
												Nom de l'option *
											</label>
											<input
												type="text"
												value={option.name}
												onChange={(e) => updateOption(index, "name", e.target.value)}
												placeholder="Ex: Équipement photo"
												className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
											/>
										</div>

										{/* Option Price */}
										<div>
											<label className="block text-sm font-medium text-gray-700 mb-1">
												Prix supplémentaire (€)
											</label>
											<input
												type="number"
												step="0.01"
												min="0"
												value={option.price}
												onChange={(e) =>
													updateOption(index, "price", parseFloat(e.target.value) || 0)
												}
												className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
											/>
											{/* Quote-based checkbox */}
											<div className="flex items-center mt-2">
												<input
													type="checkbox"
													id={`quote-based-${index}`}
													checked={option.quote_based || false}
													onChange={(e) =>
														updateOption(index, "quote_based", e.target.checked)
													}
													className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
												/>
												<label
													htmlFor={`quote-based-${index}`}
													className="ml-2 text-sm text-gray-700"
												>
													Sur devis (si prix = 0, affiche "sur devis" au lieu de "gratuit")
												</label>
											</div>
											{/* Per participant checkbox */}
											<div className="flex items-center mt-2">
												<input
													type="checkbox"
													id={`per-participant-${index}`}
													checked={option.per_participant || false}
													onChange={(e) =>
														updateOption(index, "per_participant", e.target.checked)
													}
													className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
												/>
												<label
													htmlFor={`per-participant-${index}`}
													className="ml-2 text-sm text-gray-700"
												>
													Prix par participant (sinon prix unique pour le service)
												</label>
											</div>
										</div>

										{/* Option Description */}
										<div className="md:col-span-2">
											<label className="block text-sm font-medium text-gray-700 mb-1">
												Description
											</label>
											<textarea
												value={option.description || ""}
												onChange={(e) => updateOption(index, "description", e.target.value)}
												placeholder="Description de l'option"
												rows={2}
												className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
											/>
										</div>

										{/* Option Type */}
										<div>
											<label className="block text-sm font-medium text-gray-700 mb-1">
												Type de sélection
											</label>
											<select
												value={option.type}
												onChange={(e) =>
													updateOption(index, "type", e.target.value as ServiceOptionType)
												}
												className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
											>
												<option value="optional">Optionnel</option>
												<option value="required_min">Minimum requis</option>
												<option value="single_choice">Choix unique</option>
												<option value="single_choice_required">Choix unique obligatoire</option>
											</select>
										</div>

										{/* Minimum Selections (for required_min type) */}
										{option.type === "required_min" && (
											<div>
												<label className="block text-sm font-medium text-gray-700 mb-1">
													Sélections minimum
												</label>
												<input
													type="number"
													min="1"
													value={option.min_selections || 1}
													onChange={(e) =>
														updateOption(
															index,
															"min_selections",
															parseInt(e.target.value) || 1
														)
													}
													className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
												/>
											</div>
										)}
									</div>

									{/* Remove Option Button */}
									<div className="flex justify-end mt-3">
										<button
											type="button"
											onClick={() => removeOption(index)}
											className="text-red-600 hover:text-red-800 text-sm flex items-center gap-1"
										>
											<X className="w-4 h-4" />
											Supprimer l'option
										</button>
									</div>
								</div>
							))}

							{/* Add New Option Button */}
							<button
								type="button"
								onClick={addOption}
								className="w-full px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-emerald-400 hover:text-emerald-600 flex items-center justify-center gap-2"
							>
								<Plus className="w-4 h-4" />
								Ajouter une option
							</button>
						</div>
						<p className="text-xs text-gray-500 mt-2">
							Configurez les options supplémentaires disponibles pour ce service
						</p>
					</div>
				</div>

				{/* Action Buttons */}
				<div className="flex justify-end gap-4 mt-8 pt-6 border-t">
					<Button variant="outline" onClick={handleCancel} disabled={saving}>
						Annuler
					</Button>
					<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
						{saving ? "Sauvegarde..." : "Sauvegarder les modifications"}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default ServiceEdit;
